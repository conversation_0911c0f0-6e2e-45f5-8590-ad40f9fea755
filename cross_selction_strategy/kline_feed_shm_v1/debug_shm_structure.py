#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享内存结构调试脚本
"""

import os
import struct
import time
from datetime import datetime

def debug_shm_structure():
    """调试共享内存结构"""
    shm_file_path = "/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm"
    
    if not os.path.exists(shm_file_path):
        print(f"共享内存文件不存在: {shm_file_path}")
        return
    
    with open(shm_file_path, 'rb') as f:
        # 读取前128字节进行分析
        data = f.read(128)
        
        print("=== 共享内存二进制数据分析 ===")
        print(f"文件大小: {os.path.getsize(shm_file_path)} 字节")
        print()
        
        # 以16进制显示前64字节
        print("前64字节的16进制数据:")
        for i in range(0, min(64, len(data)), 16):
            hex_str = ' '.join(f'{b:02x}' for b in data[i:i+16])
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
            print(f"{i:04x}: {hex_str:<48} {ascii_str}")
        print()
        
        # 尝试不同的解析方式
        print("=== 尝试不同的解析方式 ===")
        
        # 方式1: 按照头文件定义解析
        if len(data) >= 60:
            try:
                header = struct.unpack('<IIIIIQQ6I', data[:60])
                print("方式1 - 按头文件定义 (<IIIIIQQ6I):")
                print(f"  magic: 0x{header[0]:x}")
                print(f"  version: {header[1]}")
                print(f"  max_contracts: {header[2]}")
                print(f"  max_klines_per_contract: {header[3]}")
                print(f"  contract_count: {header[4]}")
                print(f"  last_kline_close_time: {header[5]} (0x{header[5]:x})")
                print(f"  last_update_timestamp: {header[6]} (0x{header[6]:x})")
                print()
                
                # 尝试解析时间戳
                if header[5] > 0:
                    # 尝试不同的时间戳格式
                    formats = [
                        ("毫秒", header[5] / 1000),
                        ("微秒", header[5] / 1000000),
                        ("纳秒", header[5] / 1000000000),
                        ("秒", header[5]),
                    ]
                    
                    print("last_kline_close_time 可能的时间格式:")
                    for name, timestamp in formats:
                        try:
                            if 0 < timestamp < 2**31:  # 合理的时间戳范围
                                dt = datetime.fromtimestamp(timestamp)
                                print(f"  {name}: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
                        except:
                            pass
                    print()
                
            except Exception as e:
                print(f"方式1解析失败: {e}")
        
        # 方式2: 尝试不同的字节序
        if len(data) >= 60:
            try:
                header = struct.unpack('>IIIIIQQ6I', data[:60])  # 大端序
                print("方式2 - 大端序 (>IIIIIQQ6I):")
                print(f"  magic: 0x{header[0]:x}")
                print(f"  last_kline_close_time: {header[5]} (0x{header[5]:x})")
                print(f"  last_update_timestamp: {header[6]} (0x{header[6]:x})")
                print()
            except Exception as e:
                print(f"方式2解析失败: {e}")
        
        # 方式3: 手动解析时间戳字段
        print("方式3 - 手动解析时间戳字段:")
        if len(data) >= 36:
            # 从偏移20开始读取两个8字节的时间戳
            time1_bytes = data[20:28]
            time2_bytes = data[28:36]
            
            time1_le = struct.unpack('<Q', time1_bytes)[0]
            time1_be = struct.unpack('>Q', time1_bytes)[0]
            time2_le = struct.unpack('<Q', time2_bytes)[0]
            time2_be = struct.unpack('>Q', time2_bytes)[0]
            
            print(f"  偏移20-28字节 (小端): {time1_le} (0x{time1_le:x})")
            print(f"  偏移20-28字节 (大端): {time1_be} (0x{time1_be:x})")
            print(f"  偏移28-36字节 (小端): {time2_le} (0x{time2_le:x})")
            print(f"  偏移28-36字节 (大端): {time2_be} (0x{time2_be:x})")
            print()
            
            # 检查哪个看起来像合理的时间戳
            current_time_ms = int(time.time() * 1000)
            print(f"当前时间戳(毫秒): {current_time_ms}")
            
            candidates = [
                ("偏移20小端", time1_le),
                ("偏移20大端", time1_be), 
                ("偏移28小端", time2_le),
                ("偏移28大端", time2_be),
            ]
            
            print("可能的时间戳候选:")
            for name, ts in candidates:
                # 检查是否在合理范围内（2020-2030年的毫秒时间戳）
                if 1577836800000 <= ts <= 1893456000000:  # 2020-01-01 到 2030-01-01
                    try:
                        dt = datetime.fromtimestamp(ts / 1000)
                        diff = (current_time_ms - ts) / 1000
                        print(f"  {name}: {dt.strftime('%Y-%m-%d %H:%M:%S')} (时间差: {diff:.1f}秒)")
                    except:
                        pass

if __name__ == "__main__":
    debug_shm_structure()
