#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享内存监控功能测试脚本
"""

import os
import sys
import struct
import time
from datetime import datetime

def read_shm_header(shm_file_path):
    """读取共享内存头部信息"""
    try:
        if not os.path.exists(shm_file_path):
            print(f"共享内存文件不存在: {shm_file_path}")
            return None

        with open(shm_file_path, 'rb') as f:
            # 读取完整的头部结构（60字节）
            # struct kline_shm_header {
            #   uint32_t magic;                   // 4 bytes
            #   uint32_t version;                 // 4 bytes
            #   uint32_t max_contracts;           // 4 bytes
            #   uint32_t max_klines_per_contract; // 4 bytes
            #   uint32_t contract_count;          // 4 bytes
            #   uint64_t last_kline_close_time;   // 8 bytes
            #   uint64_t last_update_timestamp;   // 8 bytes
            #   uint32_t reserved[6];             // 24 bytes
            # }
            header_data = f.read(60)
            if len(header_data) < 60:
                print(f"共享内存文件太小: {len(header_data)} < 60字节")
                return None

            # 解析头部数据 (小端序)
            # 格式: 5个uint32_t + 2个uint64_t + 6个uint32_t
            header = struct.unpack('<IIIIIQQ6I', header_data)

            magic = header[0]
            version = header[1]
            max_contracts = header[2]
            max_klines_per_contract = header[3]
            contract_count = header[4]
            last_kline_close_time = header[5]
            last_update_timestamp = header[6]

            print(f"调试信息: magic=0x{magic:x}, version={version}, contracts={contract_count}")
            print(f"调试信息: last_kline_close_time={last_kline_close_time}, last_update_timestamp={last_update_timestamp}")

            return {
                'magic': magic,
                'version': version,
                'max_contracts': max_contracts,
                'max_klines_per_contract': max_klines_per_contract,
                'contract_count': contract_count,
                'last_kline_close_time': last_kline_close_time,
                'last_update_timestamp': last_update_timestamp
            }
            
    except Exception as e:
        print(f"读取共享内存头部失败: {e}")
        return None

def format_timestamp(timestamp_ns):
    """格式化时间戳（纳秒）为可读字符串"""
    if timestamp_ns == 0:
        return "未设置"

    # 检查时间戳是否合理
    if timestamp_ns > 2**63 - 1 or timestamp_ns < 0:
        return f"无效时间戳: {timestamp_ns}"

    timestamp_s = timestamp_ns / 1e9

    # 检查转换后的时间戳是否合理
    if timestamp_s > 2**31 - 1 or timestamp_s < 0:
        return f"无效时间戳: {timestamp_ns} (转换后: {timestamp_s})"

    try:
        dt = datetime.fromtimestamp(timestamp_s)
        return dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 保留毫秒
    except (ValueError, OSError) as e:
        return f"时间戳解析错误: {timestamp_ns} ({e})"

def format_kline_time(timestamp_ms):
    """格式化K线时间戳（毫秒）为可读字符串"""
    if timestamp_ms == 0:
        return "未设置"

    # 检查时间戳是否合理（应该在合理的范围内）
    if timestamp_ms > 2**63 - 1 or timestamp_ms < 0:
        return f"无效时间戳: {timestamp_ms}"

    # 如果时间戳太大，可能是纳秒而不是毫秒
    if timestamp_ms > 1e15:  # 大于2001年的纳秒时间戳
        timestamp_s = timestamp_ms / 1e9  # 纳秒转秒
    else:
        timestamp_s = timestamp_ms / 1000  # 毫秒转秒

    # 检查转换后的时间戳是否合理
    if timestamp_s > 2**31 - 1 or timestamp_s < 0:
        return f"无效时间戳: {timestamp_ms} (转换后: {timestamp_s})"

    try:
        dt = datetime.fromtimestamp(timestamp_s)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except (ValueError, OSError) as e:
        return f"时间戳解析错误: {timestamp_ms} ({e})"

def check_timeout(last_update_ns, timeout_seconds=64):
    """检查是否超时"""
    if last_update_ns == 0:
        return False, 0
        
    current_time_ns = time.time_ns()
    time_diff_seconds = (current_time_ns - last_update_ns) / 1e9
    
    return time_diff_seconds > timeout_seconds, time_diff_seconds

def test_shm_monitor():
    """测试共享内存监控功能"""
    shm_file_path = "/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm"
    
    print("=== 共享内存监控测试 ===")
    print(f"共享内存文件路径: {shm_file_path}")
    print()
    
    # 读取头部信息
    header = read_shm_header(shm_file_path)
    if header is None:
        print("❌ 无法读取共享内存头部")
        return False
    
    # 验证魔数
    expected_magic = 0x4B4C494E  # "KLIN" in ASCII
    if header['magic'] != expected_magic:
        print(f"❌ 魔数不匹配，期望: {expected_magic:x}, 实际: {header['magic']:x}")
        return False
    
    print("✅ 共享内存文件格式正确")
    print()
    
    # 显示头部信息
    print("=== 共享内存头部信息 ===")
    print(f"魔数: 0x{header['magic']:x}")
    print(f"版本: {header['version']}")
    print(f"最大合约数: {header['max_contracts']}")
    print(f"每合约最大K线数: {header['max_klines_per_contract']}")
    print(f"当前合约数: {header['contract_count']}")
    print(f"最后K线收盘时间: {format_kline_time(header['last_kline_close_time'])}")
    print(f"最后更新时间戳: {format_timestamp(header['last_update_timestamp'])}")
    print()
    
    # 检查超时
    is_timeout, time_diff = check_timeout(header['last_update_timestamp'])
    
    print("=== 超时检查结果 ===")
    print(f"时间差: {time_diff:.2f}秒")
    print(f"超时阈值: 64秒")
    
    if is_timeout:
        print("❌ 检测到超时！")
    else:
        print("✅ 更新正常")
    
    return True

def continuous_monitor():
    """持续监控模式"""
    shm_file_path = "/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm"
    
    print("=== 持续监控模式 ===")
    print("按Ctrl+C退出")
    print()
    
    try:
        while True:
            header = read_shm_header(shm_file_path)
            if header is None:
                print(f"{datetime.now().strftime('%H:%M:%S')} - 无法读取共享内存")
            else:
                is_timeout, time_diff = check_timeout(header['last_update_timestamp'])
                status = "超时" if is_timeout else "正常"
                print(f"{datetime.now().strftime('%H:%M:%S')} - "
                      f"合约数: {header['contract_count']}, "
                      f"时间差: {time_diff:.1f}s, "
                      f"状态: {status}")
            
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\n监控已停止")

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--monitor":
        continuous_monitor()
    else:
        success = test_shm_monitor()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
