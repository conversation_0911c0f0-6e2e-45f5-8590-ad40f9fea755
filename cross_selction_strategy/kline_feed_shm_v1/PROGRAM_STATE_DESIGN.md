# K线数据处理程序状态管理设计

## 概述

为了让Python监控脚本能够智能地判断是否进行超时检测，我们在共享内存头部新增了`program_state`字段来维护程序运行状态。

## 状态定义

```cpp
enum class ProgramState : uint32_t {
  INIT = 0,       // 初始化状态 - 程序正在启动和初始化，不进行超时检测
  RUNNING = 1,    // 正常运行状态 - 程序正常运行，进行超时检测
  STOP = 2,       // 停止状态 - 程序正常停止，不进行超时检测
  ERROR = 3       // 错误状态 - 程序遇到错误，不进行超时检测
};
```

## 共享内存结构变更

### 修改前
```cpp
struct kline_shm_header {
  uint32_t magic;
  uint32_t version;
  uint32_t max_contracts;
  uint32_t max_klines_per_contract;
  uint32_t contract_count;
  uint64_t last_kline_close_time;
  uint64_t last_update_timestamp;
  uint32_t reserved[6];           // 6个保留字段
};
```

### 修改后
```cpp
struct kline_shm_header {
  uint32_t magic;
  uint32_t version;
  uint32_t max_contracts;
  uint32_t max_klines_per_contract;
  uint32_t contract_count;
  uint64_t last_kline_close_time;
  uint64_t last_update_timestamp;
  uint32_t program_state;         // 新增：程序状态字段
  uint32_t reserved[5];           // 减少为5个保留字段
};
```

## C++端状态维护逻辑

### 1. INIT状态设置
**位置**: `shm_kline_manager::create_shm()`
**时机**: 共享内存文件首次创建时
```cpp
shm_ptr_->header.program_state = static_cast<uint32_t>(ProgramState::INIT);
```

### 2. RUNNING状态设置
**位置**: `strategy::process_cached_klines()`
**时机**: 完成所有HTTP数据接收和merge后
```cpp
kline_manager_->set_program_state(ProgramState::RUNNING);
```

**触发条件**:
- 所有HTTP请求完成
- HTTP数据和WebSocket数据merge完成
- 数据已flush到共享内存

### 3. STOP状态设置
**位置**: `strategy::~strategy()`
**时机**: 程序析构时
```cpp
kline_manager_->set_program_state(ProgramState::STOP);
```

## Python端监控逻辑

### 结构定义
```python
class KlineShmHeader(ctypes.Structure):
    _fields_ = [
        ("magic", ctypes.c_uint32),
        ("version", ctypes.c_uint32),
        ("max_contracts", ctypes.c_uint32),
        ("max_klines_per_contract", ctypes.c_uint32),
        ("contract_count", ctypes.c_uint32),
        ("last_kline_close_time", ctypes.c_uint64),
        ("last_update_timestamp", ctypes.c_uint64),
        ("program_state", ctypes.c_uint32),  # 新增字段
        ("reserved", ctypes.c_uint32 * 5)   # 减少为5个
    ]
```

### 超时检测逻辑
```python
def check_shm_update_timeout(self):
    header = self.read_shm_header()
    program_state = header['program_state']
    
    # 只在RUNNING状态下进行超时检测
    if program_state != ShmProgramState.RUNNING:
        return False  # 跳过超时检测
    
    # 进行正常的超时检测逻辑
    # ...
```

## 状态转换流程

```
程序启动
    ↓
[INIT] ← 共享内存初始化
    ↓
HTTP请求阶段（订阅WebSocket，发送HTTP请求）
    ↓
数据接收和缓存阶段
    ↓
数据merge和flush完成
    ↓
[RUNNING] ← 正常运行状态
    ↓
程序退出信号
    ↓
[STOP] ← 程序析构
```

## 监控脚本行为

| 程序状态 | 监控脚本行为 | 说明 |
|---------|-------------|------|
| INIT | 跳过超时检测 | 程序正在初始化，数据还未稳定 |
| RUNNING | 进行超时检测 | 程序正常运行，监控K线更新 |
| STOP | 跳过超时检测 | 程序正常停止，避免误报 |
| ERROR | 跳过超时检测 | 程序异常，避免重复重启 |

## 实现细节

### C++端方法
```cpp
// 设置程序状态
void shm_kline_manager::set_program_state(ProgramState state);

// 获取程序状态
ProgramState shm_kline_manager::get_program_state() const;
```

### Python端状态枚举
```python
class ShmProgramState:
    INIT = 0
    RUNNING = 1
    STOP = 2
    ERROR = 3
```

## 优势

1. **智能监控**: 避免在初始化阶段误触发超时重启
2. **状态感知**: 监控脚本能够理解程序当前状态
3. **稳定性**: 减少不必要的程序重启
4. **可扩展**: 预留了ERROR状态用于异常处理

## 测试验证

可以通过以下方式验证状态维护：

```bash
# 运行测试脚本查看当前状态
python3 test_shm_monitor.py

# 持续监控状态变化
python3 test_shm_monitor.py --monitor
```

预期看到状态变化：
1. 程序启动时：`INIT (0)`
2. 初始化完成后：`RUNNING (1)`
3. 程序停止时：`STOP (2)`
