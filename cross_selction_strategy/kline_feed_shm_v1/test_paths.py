#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的路径检查测试脚本
"""

import os
import sys

def test_paths():
    """测试所有路径是否存在"""
    git_root = "/home/<USER>/git"
    
    paths_to_check = [
        ("Git根目录", git_root),
        ("fast_trader_elite可执行文件", os.path.join(git_root, "fast_trader_elite_pkg/main/fast_trader_elite")),
        ("auto_instrument脚本", os.path.join(git_root, "fast_trader_elite_pkg/auto_instrument/auto_instrument.sh")),
        ("auto_instrument目录", os.path.join(git_root, "fast_trader_elite_pkg/auto_instrument")),
        ("kline_feed配置文件", os.path.join(git_root, "cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json")),
        ("fix_md配置文件", os.path.join(git_root, "fast_trader_elite_pkg/config/fix_md.json")),
    ]
    
    print("=== 路径检查结果 ===")
    all_exist = True
    
    for name, path in paths_to_check:
        exists = os.path.exists(path)
        status = "✓" if exists else "✗"
        print(f"{status} {name}: {path}")
        if not exists:
            all_exist = False
    
    print("\n=== 总结 ===")
    if all_exist:
        print("✓ 所有路径都存在，脚本应该可以正常运行")
    else:
        print("✗ 有路径不存在，需要检查配置")
    
    return all_exist

if __name__ == "__main__":
    success = test_paths()
    sys.exit(0 if success else 1)
