# K线数据处理程序运行脚本使用说明

## 概述

`run_kline_programs.py` 是一个Python脚本，用于自动化运行K线数据处理相关的程序：

1. **kline_feed_shm_v1**: 负责K线数据落地到共享内存
2. **fix_md_tools**: 负责修复共享内存中的K线数据

## 功能特点

- 支持同时运行两个程序或单独运行某个程序
- 自动进程监控和管理
- 详细的日志记录
- 优雅的进程退出处理
- 可配置的路径设置
- 信号处理支持（Ctrl+C安全退出）

## 使用方法

### 基本用法

```bash
# 同时运行两个程序（默认模式）
python3 run_kline_programs.py

# 或者
python3 run_kline_programs.py --mode both
```

### 单独运行程序

```bash
# 只运行kline_feed_shm_v1程序
python3 run_kline_programs.py --mode kline_feed

# 只运行fix_md_tools程序
python3 run_kline_programs.py --mode fix_md
```

### 自定义Git根目录

```bash
# 指定不同的Git根目录
python3 run_kline_programs.py --git-root /path/to/your/git/root
```

### 命令行参数

- `--mode`: 运行模式
  - `both`: 同时运行两个程序（默认）
  - `kline_feed`: 只运行kline_feed_shm_v1程序
  - `fix_md`: 只运行fix_md_tools程序

- `--git-root`: Git根目录路径（默认: `/home/<USER>/git`）

## 路径配置

脚本中的关键路径都通过变量配置，方便修改：

```python
# Git根目录
self.git_root = "/home/<USER>/git"

# fast_trader_elite可执行文件路径
self.fast_trader_elite_path = os.path.join(self.git_root, "fast_trader_elite_pkg/main/fast_trader_elite")

# kline_feed_shm_v1配置文件路径
self.kline_feed_config = os.path.join(self.git_root, "cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json")

# fix_md_tools配置文件路径
self.fix_md_config = os.path.join(self.git_root, "fast_trader_elite_pkg/config/fix_md.json")

# 日志目录
self.log_dir = os.path.join(self.git_root, "cross_selction_strategy/kline_feed_shm_v1/log")
```

## 日志管理

- 脚本运行日志保存在 `log/runner_YYYYMMDD_HHMMSS.log`
- kline_feed_shm_v1程序日志保存在 `log/kline_feed_shm_YYYYMMDD_HHMMSS.log`
- fix_md_tools程序日志保存在 `log/fix_md_tools_YYYYMMDD_HHMMSS.log`
- 同时在控制台输出日志信息

## 进程管理

- 脚本会自动监控子进程状态
- 支持优雅退出（先发送SIGTERM，10秒后强制SIGKILL）
- 按Ctrl+C可以安全退出所有进程
- 进程意外退出时会记录日志

## 运行流程

### 同时运行模式（both）

1. 检查所有必要路径是否存在
2. 启动kline_feed_shm_v1程序
3. 等待5秒让程序初始化
4. 启动fix_md_tools程序
5. 启动监控线程
6. 等待用户中断或进程退出

### 单独运行模式

1. 检查所有必要路径是否存在
2. 启动指定的程序
3. 等待用户中断或进程退出

## 注意事项

1. 确保所有配置文件路径正确
2. 确保fast_trader_elite可执行文件存在且有执行权限
3. 确保有足够的权限创建日志文件
4. 运行前建议检查共享内存相关设置

## 故障排除

### 常见问题

1. **路径不存在错误**
   - 检查Git根目录是否正确
   - 确认所有配置文件和可执行文件存在

2. **权限错误**
   - 确保脚本有执行权限：`chmod +x run_kline_programs.py`
   - 确保fast_trader_elite有执行权限

3. **进程启动失败**
   - 查看详细日志信息
   - 检查配置文件格式是否正确
   - 确认依赖的共享库文件存在

### 调试方法

1. 查看脚本运行日志
2. 查看各程序的输出日志
3. 手动运行命令验证：
   ```bash
   cd /home/<USER>/git/fast_trader_elite_pkg/main
   ./fast_trader_elite ../config/kline_feed_shm.json
   ./fast_trader_elite ../config/fix_md.json
   ```

## 示例

```bash
# 进入脚本目录
cd /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1

# 运行脚本
python3 run_kline_programs.py

# 查看日志
tail -f log/runner_*.log
tail -f log/kline_feed_shm_*.log
tail -f log/fix_md_tools_*.log
```
