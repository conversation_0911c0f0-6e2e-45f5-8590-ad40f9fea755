# K线数据处理程序调度脚本使用说明

## 概述

`run_kline_programs.py` 是一个智能调度脚本，不仅按严格顺序运行K线数据处理程序，还具备共享内存监控和自动重启功能：

1. **auto_instrument.sh**: 自动配置工具脚本（运行完成后退出）
2. **fix_md_tools**: 修复共享内存中的K线数据（运行完成后退出）
3. **kline_feed_shm_v1**: K线数据落地到共享内存（常驻进程）

## 核心功能

### 🔄 智能调度
- 严格按照 auto_instrument → fix_md → kline_feed 的顺序执行
- 前两个程序完成后才启动下一个，最后一个为常驻进程

### 📊 共享内存监控
- 实时监控 `kline_data.shm` 文件的更新状态
- 检测共享内存头部的 `last_kline_close_time` 字段（毫秒时间戳）
- 解析程序状态（INIT/RUNNING/STOP/ERROR）
- 智能超时检测：只在RUNNING状态下进行超时检测
- 默认64秒超时检测（可配置）

### 🔧 自动重启机制
- 当检测到共享内存超过64秒未更新时，自动重启所有程序
- 智能状态感知：INIT状态下不触发超时检测
- 优雅终止当前进程，然后重新执行完整的启动流程
- 确保系统持续稳定运行

## 功能特点

- 🔄 **智能调度**: 严格按顺序运行三个程序：auto_instrument -> fix_md -> kline_feed_shm
- 📊 **共享内存监控**: 实时检测kline_data.shm文件更新状态
- 🔧 **自动重启**: 检测到超时时自动重启所有程序
- 🎯 **单独运行**: 支持单独运行某个程序
- 📝 **详细日志**: 完整的运行和监控日志记录
- 🛡️ **优雅退出**: 安全的进程启停和异常处理
- ⚙️ **可配置**: 路径、超时时间等参数可配置
- 🔌 **信号处理**: 支持Ctrl+C安全退出

## 使用方法

### 基本用法

```bash
# 按顺序运行所有程序（默认模式）
python3 run_kline_programs.py

# 或者
python3 run_kline_programs.py --mode all
```

### 单独运行程序

```bash
# 只运行auto_instrument.sh脚本
python3 run_kline_programs.py --mode auto_instrument

# 只运行kline_feed_shm_v1程序
python3 run_kline_programs.py --mode kline_feed

# 只运行fix_md_tools程序
python3 run_kline_programs.py --mode fix_md
```

### 高级配置

```bash
# 指定不同的Git根目录
python3 run_kline_programs.py --git-root /path/to/your/git/root

# 禁用共享内存监控
python3 run_kline_programs.py --disable-flush-check

# 自定义超时时间（120秒）
python3 run_kline_programs.py --flush-timeout 120

# 组合使用
python3 run_kline_programs.py --mode all --flush-timeout 90 --git-root /custom/path
```

### 命令行参数

- `--mode`: 运行模式
  - `all`: 按顺序运行所有程序（默认）
  - `auto_instrument`: 只运行auto_instrument.sh脚本
  - `kline_feed`: 只运行kline_feed_shm_v1程序
  - `fix_md`: 只运行fix_md_tools程序

- `--git-root`: Git根目录路径（默认: `/home/<USER>/git`）

- `--disable-flush-check`: 禁用共享内存flush检查

- `--flush-timeout`: 共享内存更新超时时间（秒，默认64秒）

## 路径配置

脚本中的关键路径都通过变量配置，方便修改：

```python
# Git根目录
self.git_root = "/home/<USER>/git"

# fast_trader_elite可执行文件路径
self.fast_trader_elite_path = os.path.join(self.git_root, "fast_trader_elite_pkg/main/fast_trader_elite")

# auto_instrument相关路径
self.auto_instrument_dir = os.path.join(self.git_root, "fast_trader_elite_pkg/auto_instrument")
self.auto_instrument_script = os.path.join(self.auto_instrument_dir, "auto_instrument.sh")

# kline_feed_shm_v1配置文件路径
self.kline_feed_config = os.path.join(self.git_root, "cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json")

# fix_md_tools配置文件路径
self.fix_md_config = os.path.join(self.git_root, "fast_trader_elite_pkg/config/fix_md.json")

# 日志目录
self.log_dir = os.path.join(self.git_root, "cross_selction_strategy/kline_feed_shm_v1/log")
```

## 日志管理

- 脚本运行日志保存在 `log/runner_YYYYMMDD_HHMMSS.log`
- auto_instrument脚本日志保存在 `log/auto_instrument_YYYYMMDD_HHMMSS.log`
- kline_feed_shm_v1程序日志保存在 `log/kline_feed_shm_YYYYMMDD_HHMMSS.log`
- fix_md_tools程序日志保存在 `log/fix_md_tools_YYYYMMDD_HHMMSS.log`
- 同时在控制台输出日志信息

## 进程管理

- 脚本会自动监控子进程状态
- 支持优雅退出（先发送SIGTERM，10秒后强制SIGKILL）
- 按Ctrl+C可以安全退出所有进程
- 进程意外退出时会记录日志

## 运行流程

### 完整运行模式（all）

1. **路径检查**: 验证所有必要文件和目录是否存在
2. **第1步**: 运行auto_instrument.sh脚本（等待完成）
3. **第2步**: 运行fix_md_tools程序（等待完成）
4. **第3步**: 启动kline_feed_shm_v1程序（常驻进程）
5. **启动监控**: 开始共享内存监控线程
6. **持续监控**:
   - 每5秒检查进程状态
   - 每5秒检查共享内存更新时间
   - 检测到超时时自动重启所有程序
7. **等待退出**: 用户中断或异常退出

### 监控和重启机制

当检测到共享内存超过设定时间（默认64秒）未更新时：

1. **记录告警**: 记录超时检测日志
2. **终止进程**: 优雅终止kline_feed_shm_v1进程
3. **重新启动**: 按完整流程重新启动所有程序
4. **继续监控**: 重启完成后继续监控

### 单独运行模式

1. 检查所有必要路径是否存在
2. 启动指定的程序
3. 对于auto_instrument和fix_md：等待程序完成
4. 对于kline_feed：启动监控并等待用户中断

## 注意事项

1. **路径配置**: 确保所有配置文件路径正确
2. **执行权限**: 确保fast_trader_elite和auto_instrument.sh有执行权限
3. **工作目录**: auto_instrument.sh必须在其所在目录中运行
4. **文件权限**: 确保有足够的权限创建日志文件和读取共享内存文件
5. **共享内存**: 运行前建议检查共享内存相关设置
6. **执行顺序**: 程序严格按照 auto_instrument → fix_md → kline_feed 的顺序执行
7. **监控机制**: 共享内存监控依赖于kline_data.shm文件的存在和正确格式
8. **超时设置**: 根据实际业务需求调整flush-timeout参数

## 故障排除

### 常见问题

1. **路径不存在错误**
   - 检查Git根目录是否正确
   - 确认所有配置文件和可执行文件存在

2. **权限错误**
   - 确保脚本有执行权限：`chmod +x run_kline_programs.py`
   - 确保fast_trader_elite有执行权限

3. **进程启动失败**
   - 查看详细日志信息
   - 检查配置文件格式是否正确
   - 确认依赖的共享库文件存在

### 调试方法

1. 查看脚本运行日志
2. 查看各程序的输出日志
3. 手动运行命令验证：
   ```bash
   # 运行auto_instrument.sh
   cd /home/<USER>/git/fast_trader_elite_pkg/auto_instrument
   ./auto_instrument.sh

   # 运行fix_md_tools
   cd /home/<USER>/git/fast_trader_elite_pkg/main
   ./fast_trader_elite ../config/fix_md.json

   # 运行kline_feed_shm_v1
   ./fast_trader_elite ../../cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json
   ```

## 示例

```bash
# 进入脚本目录
cd /home/<USER>/git/cross_selction_strategy/kline_feed_shm_v1

# 运行脚本
python3 run_kline_programs.py

# 查看日志
tail -f log/runner_*.log
tail -f log/auto_instrument_*.log
tail -f log/kline_feed_shm_*.log
tail -f log/fix_md_tools_*.log
```
