#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线数据处理程序运行脚本
用于运行kline_feed_shm_v1和fix_md_tools程序

运行方式:
1. kline_feed_shm_v1: 负责K线数据落地到共享内存
2. fix_md_tools: 负责修复共享内存中的K线数据

作者: 自动生成
日期: 2024
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path
import argparse
import logging
from datetime import datetime

class KlineProgramRunner:
    def __init__(self):
        # 配置路径变量 - 可以根据需要修改这些路径
        self.git_root = "/home/<USER>/git"
        self.fast_trader_elite_path = os.path.join(self.git_root, "fast_trader_elite_pkg/main/fast_trader_elite")
        
        # kline_feed_shm_v1相关路径
        self.kline_feed_config = os.path.join(self.git_root, "cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json")
        
        # fix_md_tools相关路径  
        self.fix_md_config = os.path.join(self.git_root, "fast_trader_elite_pkg/config/fix_md.json")
        
        # 日志目录
        self.log_dir = os.path.join(self.git_root, "cross_selction_strategy/kline_feed_shm_v1/log")
        
        # 进程管理
        self.kline_feed_process = None
        self.fix_md_process = None
        self.running = False
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志配置"""
        # 确保日志目录存在
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 配置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(os.path.join(self.log_dir, f'runner_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def check_paths(self):
        """检查所有必要的路径是否存在"""
        paths_to_check = [
            ("fast_trader_elite可执行文件", self.fast_trader_elite_path),
            ("kline_feed配置文件", self.kline_feed_config),
            ("fix_md配置文件", self.fix_md_config)
        ]
        
        missing_paths = []
        for name, path in paths_to_check:
            if not os.path.exists(path):
                missing_paths.append(f"{name}: {path}")
                
        if missing_paths:
            self.logger.error("以下路径不存在:")
            for path in missing_paths:
                self.logger.error(f"  - {path}")
            return False
            
        self.logger.info("所有路径检查通过")
        return True
        
    def run_kline_feed_shm(self):
        """运行kline_feed_shm_v1程序"""
        try:
            cmd = [self.fast_trader_elite_path, self.kline_feed_config]
            log_file = os.path.join(self.log_dir, f'kline_feed_shm_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            
            self.logger.info(f"启动kline_feed_shm_v1程序...")
            self.logger.info(f"命令: {' '.join(cmd)}")
            self.logger.info(f"日志文件: {log_file}")
            
            with open(log_file, 'w') as f:
                self.kline_feed_process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    cwd=os.path.dirname(self.fast_trader_elite_path)
                )
                
            self.logger.info(f"kline_feed_shm_v1程序已启动，PID: {self.kline_feed_process.pid}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动kline_feed_shm_v1程序失败: {e}")
            return False
            
    def run_fix_md_tools(self):
        """运行fix_md_tools程序"""
        try:
            cmd = [self.fast_trader_elite_path, self.fix_md_config]
            log_file = os.path.join(self.log_dir, f'fix_md_tools_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            
            self.logger.info(f"启动fix_md_tools程序...")
            self.logger.info(f"命令: {' '.join(cmd)}")
            self.logger.info(f"日志文件: {log_file}")
            
            with open(log_file, 'w') as f:
                self.fix_md_process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    cwd=os.path.dirname(self.fast_trader_elite_path)
                )
                
            self.logger.info(f"fix_md_tools程序已启动，PID: {self.fix_md_process.pid}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动fix_md_tools程序失败: {e}")
            return False
            
    def stop_processes(self):
        """停止所有进程"""
        self.logger.info("正在停止所有进程...")
        self.running = False
        
        processes = [
            ("kline_feed_shm_v1", self.kline_feed_process),
            ("fix_md_tools", self.fix_md_process)
        ]
        
        for name, process in processes:
            if process and process.poll() is None:
                try:
                    self.logger.info(f"停止{name}程序 (PID: {process.pid})")
                    process.terminate()
                    
                    # 等待进程优雅退出
                    try:
                        process.wait(timeout=10)
                        self.logger.info(f"{name}程序已正常退出")
                    except subprocess.TimeoutExpired:
                        self.logger.warning(f"{name}程序未在10秒内退出，强制终止")
                        process.kill()
                        process.wait()
                        
                except Exception as e:
                    self.logger.error(f"停止{name}程序时出错: {e}")
                    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            try:
                # 检查kline_feed_shm进程
                if self.kline_feed_process and self.kline_feed_process.poll() is not None:
                    self.logger.warning(f"kline_feed_shm_v1程序意外退出，退出码: {self.kline_feed_process.returncode}")
                    
                # 检查fix_md_tools进程
                if self.fix_md_process and self.fix_md_process.poll() is not None:
                    self.logger.warning(f"fix_md_tools程序意外退出，退出码: {self.fix_md_process.returncode}")
                    
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                self.logger.error(f"监控进程时出错: {e}")
                time.sleep(5)
                
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在退出...")
        self.stop_processes()
        sys.exit(0)
        
    def run_both_programs(self):
        """同时运行两个程序"""
        if not self.check_paths():
            return False
            
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.running = True
        
        # 启动kline_feed_shm_v1程序
        if not self.run_kline_feed_shm():
            return False
            
        # 等待一段时间让kline_feed_shm_v1程序初始化
        self.logger.info("等待kline_feed_shm_v1程序初始化...")
        time.sleep(5)
        
        # 启动fix_md_tools程序
        if not self.run_fix_md_tools():
            self.stop_processes()
            return False
            
        # 启动监控线程
        monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        monitor_thread.start()
        
        self.logger.info("所有程序已启动，按Ctrl+C退出")
        
        try:
            # 主线程等待
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")
            
        self.stop_processes()
        return True
        
    def run_single_program(self, program_type):
        """运行单个程序"""
        if not self.check_paths():
            return False
            
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.running = True
        
        if program_type == "kline_feed":
            success = self.run_kline_feed_shm()
            process = self.kline_feed_process
        elif program_type == "fix_md":
            success = self.run_fix_md_tools()
            process = self.fix_md_process
        else:
            self.logger.error(f"未知的程序类型: {program_type}")
            return False
            
        if not success:
            return False
            
        self.logger.info(f"程序已启动，按Ctrl+C退出")
        
        try:
            # 等待进程结束
            process.wait()
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")
            
        self.stop_processes()
        return True


def main():
    parser = argparse.ArgumentParser(description='K线数据处理程序运行脚本')
    parser.add_argument('--mode', choices=['both', 'kline_feed', 'fix_md'], 
                       default='both', help='运行模式 (默认: both)')
    parser.add_argument('--git-root', default='/home/<USER>/git',
                       help='Git根目录路径 (默认: /home/<USER>/git)')
    
    args = parser.parse_args()
    
    runner = KlineProgramRunner()
    
    # 如果指定了不同的git根目录，更新路径
    if args.git_root != runner.git_root:
        runner.git_root = args.git_root
        runner.fast_trader_elite_path = os.path.join(runner.git_root, "fast_trader_elite_pkg/main/fast_trader_elite")
        runner.kline_feed_config = os.path.join(runner.git_root, "cross_selction_strategy/kline_feed_shm_v1/config/kline_feed_shm.json")
        runner.fix_md_config = os.path.join(runner.git_root, "fast_trader_elite_pkg/config/fix_md.json")
        runner.log_dir = os.path.join(runner.git_root, "cross_selction_strategy/kline_feed_shm_v1/log")
        runner.setup_logging()
    
    if args.mode == 'both':
        success = runner.run_both_programs()
    elif args.mode == 'kline_feed':
        success = runner.run_single_program('kline_feed')
    elif args.mode == 'fix_md':
        success = runner.run_single_program('fix_md')
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
