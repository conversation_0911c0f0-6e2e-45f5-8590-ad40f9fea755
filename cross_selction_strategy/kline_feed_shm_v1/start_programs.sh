#!/bin/bash

# K线数据处理程序启动脚本
# 这是一个简化的启动脚本，用于快速启动Python运行器

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Python脚本路径
PYTHON_SCRIPT="$SCRIPT_DIR/run_kline_programs.py"

# 检查Python脚本是否存在
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "错误: 找不到Python脚本 $PYTHON_SCRIPT"
    exit 1
fi

# 检查Python3是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: 系统中未找到python3"
    exit 1
fi

echo "启动K线数据处理程序..."
echo "脚本位置: $PYTHON_SCRIPT"
echo "当前目录: $(pwd)"
echo "参数: $@"
echo "----------------------------------------"

# 运行Python脚本，传递所有命令行参数
python3 "$PYTHON_SCRIPT" "$@"

# 获取退出码
EXIT_CODE=$?

echo "----------------------------------------"
if [ $EXIT_CODE -eq 0 ]; then
    echo "程序正常退出"
else
    echo "程序异常退出，退出码: $EXIT_CODE"
fi

exit $EXIT_CODE
