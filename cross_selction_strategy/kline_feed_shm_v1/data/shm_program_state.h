#pragma once

/**
 * @file shm_program_state.h
 * @brief 共享内存程序状态定义
 * 
 * 定义了K线数据落地程序的运行状态枚举，用于在共享内存头部的reserved字段中存储程序状态。
 * Python监控脚本会根据这个状态来决定是否进行超时检测。
 */

namespace kline_shm {

/**
 * @brief 程序运行状态枚举
 * 
 * 该枚举定义了K线数据落地程序的各种运行状态，存储在共享内存头部的reserved[0]字段中。
 * 
 * 状态转换流程：
 * INIT -> RUNNING -> STOP
 *   |       |
 *   v       v
 * ERROR   ERROR
 */
enum class ProgramState : uint32_t {
    INIT = 0,       ///< 初始化状态 - 程序正在启动和初始化，不进行超时检测
    RUNNING = 1,    ///< 正常运行状态 - 程序正常运行，进行超时检测
    STOP = 2,       ///< 停止状态 - 程序正常停止，不进行超时检测
    ERROR = 3       ///< 错误状态 - 程序遇到错误，不进行超时检测
};

/**
 * @brief 将程序状态转换为字符串
 * @param state 程序状态
 * @return 状态对应的字符串
 */
inline const char* to_string(ProgramState state) {
    switch (state) {
        case ProgramState::INIT:    return "INIT";
        case ProgramState::RUNNING: return "RUNNING";
        case ProgramState::STOP:    return "STOP";
        case ProgramState::ERROR:   return "ERROR";
        default:                    return "UNKNOWN";
    }
}

/**
 * @brief 检查状态是否应该进行超时检测
 * @param state 程序状态
 * @return true 如果应该进行超时检测，false 否则
 */
inline bool should_check_timeout(ProgramState state) {
    return state == ProgramState::RUNNING;
}

} // namespace kline_shm

/**
 * @brief 使用示例
 * 
 * 在C++程序中使用：
 * 
 * ```cpp
 * #include "shm_program_state.h"
 * 
 * // 设置程序状态
 * header->reserved[0] = static_cast<uint32_t>(kline_shm::ProgramState::INIT);
 * 
 * // 获取程序状态
 * auto state = static_cast<kline_shm::ProgramState>(header->reserved[0]);
 * 
 * // 检查是否应该进行超时检测
 * if (kline_shm::should_check_timeout(state)) {
 *     // 进行超时检测逻辑
 * }
 * 
 * // 打印状态
 * printf("Program state: %s\n", kline_shm::to_string(state));
 * ```
 * 
 * 状态设置建议：
 * 1. 程序启动时设置为 INIT
 * 2. 初始化完成后设置为 RUNNING
 * 3. 程序正常退出时设置为 STOP
 * 4. 遇到错误时设置为 ERROR
 */
