#include "shm_kline_manager.h"
#include <cerrno>
#include <cpp_frame/utils/date.h>
#include <cstdint>
#include <cstring>
#include <fcntl.h>
#include <filesystem>
#include <iostream>
#include <sys/mman.h>
#include <unistd.h>

namespace fast_trader_elite::strategy {

shm_kline_manager::shm_kline_manager(
    const std::string &save_dir, fast_trader_elite::strategy::logger &logger,
    const std::string &filename, uint32_t max_klines_per_contract)
    : save_dir_(save_dir), logger_(logger), shm_ptr_(nullptr),
      max_klines_per_contract_(max_klines_per_contract) {
  shm_path_ = save_dir_ + "/" + filename;
  std::cout << "shm_path_:" << shm_path_ << std::endl;
  STRA_LOG(logger_, STRA_INFO,
           "Initializing shared memory kline manager, path: {}, "
           "max_klines_per_contract: {}",
           shm_path_, max_klines_per_contract_);
}

shm_kline_manager::~shm_kline_manager() {
  if (shm_ptr_) {
    cpp_frame::shm::release_mmap_buffer(shm_ptr_, calculate_shm_size());
    shm_ptr_ = nullptr;
  }
}

bool shm_kline_manager::init() {
  // Ensure directory exists
  std::cout << "shm_path_:" << shm_path_ << std::endl;
  if (!std::filesystem::exists(save_dir_)) {
    std::filesystem::create_directories(save_dir_);
  }

  // Create shared memory
  if (!create_shm()) {
    STRA_LOG(logger_, STRA_ERROR, "Failed to create shared memory");
    return false;
  }

  initialized_ = true;
  return true;
}

bool shm_kline_manager::init(const std::vector<std::string> &instruments) {
  if (!init()) {
    return false;
  }

  // Pre-create metadata for all contracts
  std::lock_guard<std::mutex> lock(mutex_);
  for (const auto &instrument : instruments) {
    if (shm_ptr_->header.contract_count >= MAX_CONTRACTS) {
      STRA_LOG(logger_, STRA_ERROR, "Exceeded maximum contract count limit");
      return false;
    }

    // Check if contract already exists
    bool exists = false;
    for (uint32_t i = 0; i < shm_ptr_->header.contract_count; ++i) {
      if (std::string(shm_ptr_->contracts[i].instrument_name) == instrument) {
        contract_indices_[instrument] = i;
        exists = true;
        break;
      }
    }

    if (!exists) {
      uint32_t new_index = shm_ptr_->header.contract_count++;
      contract_indices_[instrument] = new_index;

      // Initialize contract metadata
      contract_metadata &metadata = shm_ptr_->contracts[new_index];
      strncpy(metadata.instrument_name, instrument.c_str(),
              sizeof(metadata.instrument_name) - 1);
      metadata.instrument_name[sizeof(metadata.instrument_name) - 1] = '\0';
      metadata.kline_count.store(0, std::memory_order_relaxed);
      metadata.head_index.store(0, std::memory_order_relaxed);
      metadata.last_timestamp = 0;

      STRA_LOG(logger_, STRA_DEBUG,
               "Pre-created metadata for contract {}, index: {}", instrument,
               new_index);
    }
  }

  return true;
}

size_t shm_kline_manager::calculate_shm_size() const {
  // Calculate shared memory size: header + contract metadata + kline data
  size_t header_size = sizeof(kline_shm_header);
  size_t contracts_size = sizeof(contract_metadata) * MAX_CONTRACTS;
  size_t data_size =
      sizeof(kline_data) * MAX_CONTRACTS * max_klines_per_contract_;
  return header_size + contracts_size + data_size;
}

kline_data *shm_kline_manager::get_kline_data_ptr(uint32_t contract_index,
                                                  uint32_t kline_index) {
  // Calculate kline data position in flexible array
  size_t offset = contract_index * max_klines_per_contract_ + kline_index;
  return &shm_ptr_->data[offset];
}

bool shm_kline_manager::create_shm() {
  // Create shared memory using calculated size
  size_t shm_size = calculate_shm_size();

  // Manually create memory mapped file
  int shm_fd = open(shm_path_.c_str(), O_RDWR | O_CREAT, 0666);
  if (shm_fd == -1) {
    STRA_LOG(logger_, STRA_ERROR,
             "Failed to open shared memory file: {}, error: {}", shm_path_,
             strerror(errno));
    return false;
  }

  if (ftruncate(shm_fd, shm_size) != 0) {
    STRA_LOG(logger_, STRA_ERROR,
             "Failed to set shared memory file size: {}, size: {}, error: {}",
             shm_path_, shm_size, strerror(errno));
    close(shm_fd);
    return false;
  }

  fallocate(shm_fd, 0, 0, shm_size);

  shm_ptr_ = (kline_shm *)mmap(0, shm_size, PROT_READ | PROT_WRITE,
                               MAP_SHARED | MAP_POPULATE, shm_fd, 0);
  close(shm_fd);

  if (shm_ptr_ == MAP_FAILED) {
    STRA_LOG(logger_, STRA_ERROR, "Memory mapping failed: {}, error: {}",
             shm_path_, strerror(errno));
    return false;
  }

  // Initialize shared memory header
  if (shm_ptr_->header.magic != KLINE_SHM_MAGIC) {
    // Newly created shared memory, initialize header
    shm_ptr_->header.magic = KLINE_SHM_MAGIC;
    shm_ptr_->header.version = KLINE_SHM_VERSION;
    shm_ptr_->header.max_contracts = MAX_CONTRACTS;
    shm_ptr_->header.max_klines_per_contract = max_klines_per_contract_;
    shm_ptr_->header.contract_count = 0;
    shm_ptr_->header.last_kline_close_time = 0;
    memset(shm_ptr_->header.reserved, 0, sizeof(shm_ptr_->header.reserved));

    STRA_LOG(logger_, STRA_INFO, "Initialized shared memory header");
  } else {
    // Existing shared memory, load contract indices
    for (uint32_t i = 0; i < shm_ptr_->header.contract_count; ++i) {
      std::string instrument(shm_ptr_->contracts[i].instrument_name);
      contract_indices_[instrument] = i;
      STRA_LOG(logger_, STRA_DEBUG, "Loaded contract index: {} -> {}",
               instrument, i);
    }

    STRA_LOG(logger_, STRA_INFO,
             "Loaded existing shared memory, contract count: {}",
             shm_ptr_->header.contract_count);
  }

  return true;
}

void shm_kline_manager::add_kline_data(const kline_market_data_field *kline) {
  if (!initialized_) {
    STRA_LOG(logger_, STRA_ERROR,
             "Shared memory kline manager not initialized");
    return;
  }

  std::string instrument(kline->instrument_name);
  uint64_t current_timestamp = kline->start_time;

  // Check time continuity
  auto last_ts_it = last_timestamps_.find(instrument);
  if (last_ts_it != last_timestamps_.end()) {
    uint64_t last_timestamp = last_ts_it->second;
    uint64_t expected_next_timestamp =
        last_timestamp + 60 * 1000; // 1 minute = 60000 milliseconds

    // Check for duplicate data
    if (current_timestamp == last_timestamp) {
      STRA_LOG(logger_, STRA_WARN,
               "Detected duplicate kline data, contract: {}, timestamp: {}, "
               "skipping",
               instrument, current_timestamp);
      return;
    }

    // Check if time goes backward (out-of-order data)
    if (current_timestamp < expected_next_timestamp) {
      STRA_LOG(logger_, STRA_WARN,
               "Detected out-of-order kline data, contract: {}, expected "
               "timestamp: {}, actual timestamp: {}, skipping",
               instrument, expected_next_timestamp, current_timestamp);
      return;
    }

    // Check for missing kline data
    if (current_timestamp > expected_next_timestamp) {
      uint64_t missing_timestamp = expected_next_timestamp;
      int missing_count = 0;
      kline_cache &cache = kline_caches_[instrument];

      // Fill missing data with NaN
      while (missing_timestamp < current_timestamp) {
        cache.add(missing_timestamp, instrument,
                  std::numeric_limits<double>::quiet_NaN(),
                  std::numeric_limits<double>::quiet_NaN(),
                  std::numeric_limits<double>::quiet_NaN(),
                  std::numeric_limits<double>::quiet_NaN(),
                  std::numeric_limits<double>::quiet_NaN());

        missing_timestamp += 60 * 1000; // Next minute
        missing_count++;
      }

      STRA_LOG(logger_, STRA_WARN,
               "Detected missing kline data, contract: {}, last timestamp: {}, "
               "current timestamp: {}, filled {} NaN records",
               instrument, last_timestamp, current_timestamp, missing_count);
    }
  } else {
    STRA_LOG(logger_, STRA_DEBUG,
             "Adding first kline data, contract: {}, timestamp: {}", instrument,
             current_timestamp);
  }

  // Add normal data
  kline_cache &cache = kline_caches_[instrument];
  cache.add(current_timestamp, instrument, kline->open, kline->high, kline->low,
            kline->close, kline->volume);

  // Update last timestamp
  last_timestamps_[instrument] = current_timestamp;

  // If cache reaches maximum size, write to shared memory
  if (cache.size() >= max_cache_size_) {
    write_data_to_shm(instrument);
    cache.clear();
  }
}

bool shm_kline_manager::flush_to_shm() {
  if (!initialized_) {
    STRA_LOG(logger_, STRA_ERROR,
             "Shared memory kline manager not initialized");
    return false;
  }

  // Write all cached data to shared memory
  for (auto &[instrument, cache] : kline_caches_) {
    if (cache.size() > 0) {
      write_data_to_shm(instrument);
      cache.clear();
    }
  }
  STRA_LOG(logger_, STRA_DEBUG, "Flushed all data to shared memory");

  return true;
}

bool shm_kline_manager::flush_to_shm(const std::string &ins) {
  if (!initialized_) {
    STRA_LOG(logger_, STRA_ERROR,
             "Shared memory kline manager not initialized");
    return false;
  }

  auto it = kline_caches_.find(ins);
  if (it != kline_caches_.end() && it->second.size() > 0) {
    write_data_to_shm(ins);
    it->second.clear();
  }
  return true;
}

void shm_kline_manager::write_data_to_shm(const std::string &instrument) {
  if (!shm_ptr_) {
    STRA_LOG(logger_, STRA_ERROR, "Shared memory not initialized");
    return;
  }

  // Find contract index - part that needs locking
  uint32_t contract_index;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = contract_indices_.find(instrument);
    if (it == contract_indices_.end()) {
      // If contract doesn't exist, add new contract
      if (shm_ptr_->header.contract_count >= MAX_CONTRACTS) {
        STRA_LOG(logger_, STRA_ERROR, "Exceeded maximum contract count limit");
        return;
      }

      uint32_t new_index = shm_ptr_->header.contract_count++;
      contract_indices_[instrument] = new_index;

      // Initialize contract metadata
      contract_metadata &metadata = shm_ptr_->contracts[new_index];
      strncpy(metadata.instrument_name, instrument.c_str(),
              sizeof(metadata.instrument_name) - 1);
      metadata.instrument_name[sizeof(metadata.instrument_name) - 1] = '\0';
      metadata.kline_count.store(0, std::memory_order_relaxed);
      metadata.head_index.store(0, std::memory_order_relaxed);
      metadata.last_timestamp = 0;

      STRA_LOG(logger_, STRA_DEBUG,
               "Created new contract {} metadata, index: {}", instrument,
               new_index);

      contract_index = new_index;
    } else {
      contract_index = it->second;
    }
  }

  // Lock-free part - write kline data
  contract_metadata &metadata = shm_ptr_->contracts[contract_index];
  kline_cache &cache = kline_caches_[instrument];

  // Write kline data
  for (size_t i = 0; i < cache.size(); ++i) {
    // Calculate next position in circular buffer
    uint32_t next_index;
    uint32_t current_kline_count =
        metadata.kline_count.load(std::memory_order_relaxed);
    uint32_t current_head_index =
        metadata.head_index.load(std::memory_order_relaxed);

    if (current_kline_count < max_klines_per_contract_) {
      // Buffer not full, add to end
      next_index =
          (current_head_index + current_kline_count) % max_klines_per_contract_;
      metadata.kline_count.store(current_kline_count + 1,
                                 std::memory_order_release);
    } else {
      // Buffer full, overwrite oldest data
      next_index = current_head_index;
      uint32_t new_head_index =
          (current_head_index + 1) % max_klines_per_contract_;
      metadata.head_index.store(new_head_index, std::memory_order_release);
    }

    // Write data
    kline_data *kline = get_kline_data_ptr(contract_index, next_index);
    kline->timestamp = cache.timestamps_[i];
    kline->open = cache.open_prices_[i];
    kline->high = cache.high_prices_[i];
    kline->low = cache.low_prices_[i];
    kline->close = cache.close_prices_[i];
    kline->volume = cache.volumes_[i];

    // Update last timestamp
    metadata.last_timestamp = cache.timestamps_[i] + 60 * 1000;
  }

  // shm_ptr_->header.last_kline_close_time = 0;
}

uint64_t
shm_kline_manager::get_latest_timestamp(const std::string &instrument) {
  if (!initialized_ || !shm_ptr_) {
    STRA_LOG(logger_, STRA_ERROR,
             "Shared memory kline manager not initialized");
    return 0;
  }

  // Find contract index - this part needs locking
  uint32_t contract_index;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = contract_indices_.find(instrument);
    if (it == contract_indices_.end()) {
      STRA_LOG(logger_, STRA_DEBUG, "Contract {} does not exist", instrument);
      return 0;
    }
    contract_index = it->second;
  }

  // Lock-free timestamp reading
  contract_metadata &metadata = shm_ptr_->contracts[contract_index];
  return metadata.last_timestamp;
}

void shm_kline_manager::set_last_update_time(uint64_t timestamp, bool force) {
  if (!initialized_ || !shm_ptr_) {
    STRA_LOG(logger_, STRA_ERROR,
             "Shared memory kline manager not initialized");
    return;
  }

  // Use atomic operations to update last update time, ensuring visibility
  {
    std::lock_guard<std::mutex> lock(mutex_);
    shm_ptr_->header.last_kline_close_time = timestamp;
    shm_ptr_->header.last_update_timestamp =
        cpp_frame::date::get_current_nano_sec();
  }

  STRA_LOG(logger_, STRA_DEBUG,
           "last_kline_close_time:{} last_update_timestamp:{}", timestamp,
           shm_ptr_->header.last_update_timestamp);
}

} // namespace fast_trader_elite::strategy
