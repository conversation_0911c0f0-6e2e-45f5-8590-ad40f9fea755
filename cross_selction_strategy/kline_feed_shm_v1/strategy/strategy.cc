#include "strategy.h"
#include "cpp_frame/struct_serialize/struct_ser.h"
#include "fast_trader_elite/data_model/field.h"
#include "fast_trader_elite/data_model/type.h"
#include <algorithm>
#include <chrono>
#include <cpp_frame/utils/date.h>
#include <cstdint>
#include <fstream>
#include <thread>
namespace fast_trader_elite::strategy {

strategy::strategy() {}
strategy::~strategy() {
  // 确保将缓存中的数据写入HDF5文件
  if (kline_manager_) {
    // Set program state to STOP in destructor
    kline_manager_->set_program_state(ProgramState::STOP);
    kline_manager_->flush_to_shm();
    // kline_manager_->flush_to_hdf5();
    delete kline_manager_;
    kline_manager_ = nullptr;
  }

  if (data_) {
    delete data_;
    data_ = nullptr;
  }

  logger_.poll();
  logger_.flush();
  std::cout << "strategy desc" << std::endl;
}
bool strategy::on_start(i_strategy_ctx *ctx, const std::string &strategy_name) {
  std::cout << "on start, strategy_name: " << strategy_name << std::endl;
  ctx_ = ctx;
  strategy_instance_config config = ctx->get_strategy_config(strategy_name);
  std::string path = "../log/kline_feed_shm.log";
  SET_STRA_LOG_FILE(logger_, path.c_str());

  // Set log level
  if (config.log_level == "DEBUG") {
    SET_STRA_LOG_LEVEL(logger_, LOG_DEBUG);
  } else if (config.log_level == "INFO") {
    SET_STRA_LOG_LEVEL(logger_, LOG_INFO);
  } else if (config.log_level == "WARN") {
    SET_STRA_LOG_LEVEL(logger_, LOG_WARN);
  } else if (config.log_level == "ERROR") {
    SET_STRA_LOG_LEVEL(logger_, LOG_ERROR);
  } else {
    SET_STRA_LOG_LEVEL(logger_, LOG_DEBUG); // Default level
  }

  STRA_LOG(logger_, STRA_INFO,
           "Strategy starting, name: {}, log_file: {}, log_level: {}",
           strategy_name, path, config.log_level);

  data_ = new data_manager(logger_);

  // Use strategy config path from configuration
  if (config.strategy_config_path.empty()) {
    STRA_LOG(
        logger_, STRA_ERROR,
        "No strategy_config_path provided in configuration for strategy: {}",
        strategy_name);
    return false;
  }
  parse_config(config.strategy_config_path);
  // Create a shared memory kline manager
  std::cout << "data_->save_path:" << data_->save_path << std::endl;
  kline_manager_ = new shm_kline_manager(
      data_->save_path, logger_, "kline_data.shm", data_->kline_cnt + 100);
  // Get all instrument information before initialization
  std::vector<std::string> all_instruments;
  for (auto data : data_->datas) {
    if (data) {
      all_instruments.push_back(data->instrument);
    }
  }

  // Initialize kline manager with all instruments to pre-create metadata
  if (!kline_manager_->init(all_instruments)) {
    STRA_LOG(logger_, STRA_ERROR,
             "Failed to initialize shared memory kline manager");
    return false;
  }

  // Set program state to INIT at startup
  kline_manager_->set_program_state(ProgramState::INIT);
  STRA_LOG(logger_, STRA_INFO, "Program state set to INIT");

  kline_manager_->set_max_cache_size(1000);
  total_instruments_ = 0;
  received_klines_.clear();
  last_update_times_.clear();
  init_kline_cache_.clear();
  initialization_completed_ = false;

  // First subscribe to WebSocket
  subscribe();

  // Then prepare strategy and send HTTP requests
  perpare_strategy();

  async_timer_id_ = ctx->register_async_timer(
      100, [this]() { STRA_POLL(logger_); }, true);
  check_timer_id_ = ctx_->register_timer_with_timestamp(
      1000, [this](uint64_t current_time) { check_flush(current_time); }, true);
  return true;
}
bool strategy::on_stop(i_strategy_ctx *ctx) {
  std::cout << "Strategy stopping" << std::endl;
  STRA_LOG(logger_, STRA_DEBUG,
           "Strategy stopping, flushing kline data to shared memory");
  ctx_->unregister_timer(check_timer_id_);

  // Set program state to STOP before stopping
  if (kline_manager_) {
    kline_manager_->set_program_state(ProgramState::STOP);
    STRA_LOG(logger_, STRA_INFO, "Program state set to STOP");
    kline_manager_->flush_to_shm();
  }

  return true;
}

void strategy::parse_config(const std::string &config_path) {
  STRA_LOG(logger_, STRA_INFO, "Loading config from path: {}", config_path);
  std::ifstream ifs(config_path);
  if (!ifs.good()) {
    STRA_LOG(logger_, STRA_ERROR, "Failed to open config file, path: {}",
             config_path);
    return;
  }
  ifs >> config_json_;
  ifs.close();
  data_->init_json(config_json_);
  data_->parse_config(ctx_);
}

void strategy::process_next_batch() {
  // If there are pending requests in the current batch, wait for responses
  if (is_batch_processing_ && !pending_request_ids_.empty()) {
    STRA_LOG(logger_, STRA_DEBUG,
             "Waiting for pending requests, remaining_requests: {}",
             pending_request_ids_.size());
    return;
  }

  // If no more contracts to process, mark batch processing as complete
  if (pending_contracts_.empty()) {
    if (is_batch_processing_) {
      STRA_LOG(logger_, STRA_INFO, "All contract kline requests completed");
      // All HTTP requests are completed, now we can check if initialization is
      // complete
      check_initialization_completion();
      is_batch_processing_ = false;
    }
    return;
  }

  // Start processing a new batch
  is_batch_processing_ = true;
  pending_request_ids_.clear();
  int batch_count =
      std::min(batch_size_, static_cast<int>(pending_contracts_.size()));

  STRA_LOG(logger_, STRA_INFO, "Processing new batch, contracts_in_batch: {}",
           batch_count);

  // Send requests for this batch
  for (int i = 0; i < batch_count; ++i) {
    if (pending_contracts_.empty()) {
      break;
    }

    auto data = pending_contracts_.front();
    pending_contracts_.pop();

    if (data) {
      md_kline_req_field f;
      f.instrument_idx = data->instrument_idx;
      f.exchange_id = exchange_type::BYBIT;
      f.kline_period = kline_period_type::MINUTE1;
      f.md_id = 0;
      strncpy(f.instrument_name, data->instrument.c_str(),
              sizeof(f.instrument_name) - 1);
      strncpy(f.instrument_id, data->instrument.c_str(),
              sizeof(f.instrument_id) - 1);

      uint64_t current_time = cpp_frame::date::get_current_mil_sec();
      uint64_t latest_timestamp = contract_latest_timestamps_[data->instrument];
      if (latest_timestamp > 0) {
        f.start_time = latest_timestamp - 60 * 1000 + 1;
        f.end_time = current_time;
        f.kline_cnt =
            1000; // Number of klines to fetch per API call in time range mode
        STRA_LOG(logger_, STRA_DEBUG,
                 "Requesting klines by time range, instrument: {}, start_time: "
                 "{}, end_time: {}",
                 data->instrument, f.start_time, f.end_time);
      } else {
        f.kline_cnt = data_->kline_cnt;
        f.start_time = 0;
        f.end_time = 0;
        STRA_LOG(logger_, STRA_DEBUG,
                 "Requesting klines by count, instrument: {}, count: {}",
                 data->instrument, f.kline_cnt);
      }

      // Use incremental request ID
      int request_id = next_request_id_++;

      // Record the request ID
      pending_request_ids_.insert(request_id);
      std::cout << "strategy request_id: " << request_id << " "
                << data->instrument << std::endl;

      STRA_LOG(logger_, STRA_DEBUG,
               "Sending kline request, instrument: {}, request_id: {}",
               data->instrument, request_id);

      // Send the request
      ctx_->get_kline_by_range(&f, request_id);
    }
  }
}

void strategy::perpare_strategy() {
  auto &datas = data_->datas;
  // Get current time as end time
  uint64_t current_time = cpp_frame::date::get_current_mil_sec();

  // Clear initialization cache
  init_kline_cache_.clear();

  for (auto data : datas) {
    if (data) {
      // Initialize cache for this contract
      init_kline_cache_[data->instrument] = KlineCache();

      uint64_t latest_timestamp =
          kline_manager_->get_latest_timestamp(data->instrument);
      contract_latest_timestamps_[data->instrument] = latest_timestamp;
      pending_contracts_.push(data);

      if (latest_timestamp > 0) {
        STRA_LOG(logger_, STRA_INFO,
                 "Found latest kline timestamp, instrument: {}, "
                 "latest_timestamp: {}",
                 data->instrument, latest_timestamp);
      } else {
        STRA_LOG(
            logger_, STRA_INFO,
            "No data in HDF5 file, will request default count, instrument: {}",
            data->instrument);
      }
    }
  }

  STRA_LOG(logger_, STRA_INFO,
           "Preparing to fetch klines, contract_count: {}, current_time: {}",
           pending_contracts_.size(), current_time);

  // Start processing the first batch
  process_next_batch();
}

void strategy::subscribe() {
  auto &datas = data_->datas;
  int n_mds = 10;
  std::vector<md_sub_code_field> md_fields(n_mds);
  for (int i = 0; i < n_mds; i++) {
    md_fields[i].md_id = i;
  }

  // 计算每个md_id应该分配的合约数量
  // 注意：我们使用贪心算法分配，不需要预先计算每个md_id的合约数量
  // 记录每个md_id当前已分配的合约数量
  std::vector<int> md_contract_counts(n_mds, 0);

  // 遍历所有合约并分配到md_id
  int md_index = 0;
  for (auto data : datas) {
    if (data) {
      // 找到当前应该分配的md_id
      // 优先分配给合约数量较少的md_id
      md_index = 0;
      for (int i = 1; i < n_mds; i++) {
        if (md_contract_counts[i] < md_contract_counts[md_index]) {
          md_index = i;
        }
      }

      // 将合约添加到对应的md_id
      std::string kline_sub_code = "kline.1." + data->instrument;
      md_fields[md_index].sub_code.push_back(kline_sub_code);
      md_contract_counts[md_index]++;

      // 记录合约状态
      received_klines_[data->instrument] = false;
      last_update_times_[data->instrument] = 0;
      total_instruments_++;

      STRA_LOG(logger_, STRA_DEBUG,
               "Subscribing to instrument: {}, assigned to md_id: {}",
               data->instrument, md_index);
    }
  }

  // 发送所有订阅请求
  for (int i = 0; i < n_mds; i++) {
    if (!md_fields[i].sub_code.empty()) {
      STRA_LOG(logger_, STRA_INFO,
               "Subscribing to md_id: {}, instrument_count: {}", i,
               md_fields[i].sub_code.size());
      ctx_->subscribe(&md_fields[i]);
    }
  }

  STRA_LOG(logger_, STRA_INFO,
           "Subscribed to klines, total_instrument_count: {}",
           total_instruments_);
}
void strategy::on_kline_data(i_strategy_ctx *ctx,
                             kline_market_data_field *field) {
  if (!field->is_close) {
    return;
  }

  std::string instrument(field->instrument_name);

  if (!initialization_completed_) {
    // During initialization, cache WebSocket data
    kline_market_data_field kline_copy = *field;
    init_kline_cache_[instrument].ws_klines.push_back(kline_copy);

    STRA_LOG(logger_, STRA_DEBUG,
             "Caching WebSocket kline, instrument: {}, timestamp: {}, "
             "cache_size: {}",
             instrument, field->start_time,
             init_kline_cache_[instrument].ws_klines.size());
  } else {
    // After initialization, add directly to kline_manager
    kline_manager_->add_kline_data(field);
    kline_manager_->flush_to_shm(field->instrument_name);

    if (current_kline_timestamp_ != field->start_time) {
      current_kline_timestamp_ = field->start_time;
      for (auto &pair : received_klines_) {
        pair.second = false;
      }
      received_instruments_ = 0;
      STRA_LOG(logger_, STRA_DEBUG,
               "Starting new round of kline reception, timestamp: {}",
               current_kline_timestamp_);
    }

    if (!received_klines_[instrument]) {
      received_klines_[instrument] = true;
      received_instruments_++;
      last_update_times_[instrument] = cpp_frame::date::get_current_nano_sec();
      STRA_LOG(logger_, STRA_DEBUG,
               "Received kline data, instrument: {}, received_count: {}, "
               "total_count: {}",
               instrument, received_instruments_, total_instruments_);

      if (received_instruments_ >= total_instruments_) {
        last_kline_close_time_ = field->start_time + 60 * 1000;
        STRA_LOG(logger_, STRA_INFO,
                 "Received klines for all contracts, flushing to shm "
                 "last_kline_close_time:{}",
                 last_kline_close_time_);
        kline_manager_->set_last_update_time(last_kline_close_time_);
        for (auto &pair : received_klines_) {
          pair.second = false;
        }
        received_instruments_ = 0;
      }
    }
  }
}
void strategy::on_depth_data(i_strategy_ctx *ctx,
                             depth_market_data_field *field) {}
void strategy::on_tick_data(i_strategy_ctx *ctx,
                            tick_market_data_field *field) {}
void strategy::on_transaction_data(i_strategy_ctx *ctx,
                                   transaction_field *field) {}
void strategy::on_liquidation_data(i_strategy_ctx *ctx,
                                   liquidation_field *field) {}
void strategy::on_http_kline_data(i_strategy_ctx *ctx,
                                  kline_market_data_field *field,
                                  bool is_last) {
  if (!field->is_close) {
    return;
  }
  std::string instrument(field->instrument_name);

  if (!initialization_completed_ && !is_last) {
    kline_market_data_field kline_copy = *field;

    init_kline_cache_[instrument].http_klines.push_back(kline_copy);
    // STRA_LOG(
    //     logger_, STRA_DEBUG,
    //     "Caching HTTP kline, instrument: {}, timestamp: {}, cache_size: {}",
    //     instrument, field->start_time,
    //     init_kline_cache_[instrument].http_klines.size());
  }

  if (is_last) {
    std::string instrument(field->instrument_name);
    STRA_LOG(logger_, STRA_INFO,
             "HTTP kline data reception completed, instrument: {}, data_count: "
             "{} cur_kline:{}",
             instrument, init_kline_cache_[instrument].http_klines.size(),
             cpp_frame::struct_serialize::to_json(*field));
  }
}
void strategy::on_http_depth_data(i_strategy_ctx *ctx,
                                  depth_market_data_field *field,
                                  bool is_last) {}
void strategy::on_http_tick_data(i_strategy_ctx *ctx,
                                 tick_market_data_field *field, bool is_last) {}
void strategy::on_http_transaction_data(i_strategy_ctx *ctx,
                                        transaction_field *field,
                                        bool is_last) {}
void strategy::on_http_liquidation_data(i_strategy_ctx *ctx,
                                        liquidation_field *field,
                                        bool is_last) {}
void strategy::on_order(i_strategy_ctx *ctx, order_field *field) {}
void strategy::on_trade(i_strategy_ctx *ctx, trade_field *field) {}

void strategy::on_rtn_position(i_strategy_ctx *ctx, position_field *field) {}
void strategy::on_rtn_wallet_balance(i_strategy_ctx *ctx,
                                     wallet_balance_field *field) {}

void strategy::check_initialization_completion() {
  if (!initialization_completed_) {
    STRA_LOG(logger_, STRA_INFO,
             "All HTTP requests completed, processing cached klines");
    process_cached_klines();
    initialization_completed_ = true;
  }
}

void strategy::process_cached_klines() {
  STRA_LOG(logger_, STRA_INFO, "Processing cached klines");
  uint64_t http_last_time = 0;

  // Process all contract caches
  for (auto &pair : init_kline_cache_) {
    const std::string &instrument = pair.first;
    KlineCache &cache = pair.second;

    // Merge HTTP and WebSocket kline data
    std::vector<kline_market_data_field> merged_klines;
    merged_klines.reserve(cache.http_klines.size() + cache.ws_klines.size());

    // Copy HTTP kline data
    merged_klines.insert(merged_klines.end(), cache.http_klines.begin(),
                         cache.http_klines.end());

    // Copy WebSocket kline data
    merged_klines.insert(merged_klines.end(), cache.ws_klines.begin(),
                         cache.ws_klines.end());

    // Sort by timestamp
    std::sort(
        merged_klines.begin(), merged_klines.end(),
        [](const kline_market_data_field &a, const kline_market_data_field &b) {
          return a.start_time < b.start_time;
        });

    // Remove duplicates
    if (merged_klines.size() > 1) {
      auto it = std::unique(merged_klines.begin(), merged_klines.end(),
                            [](const kline_market_data_field &a,
                               const kline_market_data_field &b) {
                              return a.start_time == b.start_time;
                            });
      merged_klines.erase(it, merged_klines.end());
    }

    STRA_LOG(logger_, STRA_INFO,
             "Merged klines, instrument: {}, http_count: {}, ws_count: {}, "
             "merged_count: {}",
             instrument, cache.http_klines.size(), cache.ws_klines.size(),
             merged_klines.size());

    // Add merged klines to kline_manager
    for (const auto &kline : merged_klines) {
      kline_manager_->add_kline_data(&kline);
      http_last_time = kline.start_time + 60 * 1000;
    }

    // Clear cache
    cache.http_klines.clear();
    cache.ws_klines.clear();
  }
  last_kline_close_time_ = http_last_time;
  uint64_t current_time = cpp_frame::date::get_current_nano_sec();
  for (auto &pair : last_update_times_) {
    pair.second = current_time;
  }
  // Flush data to shared memory
  kline_manager_->flush_to_shm();
  kline_manager_->set_last_update_time(http_last_time);

  // Set program state to RUNNING after initialization is complete
  kline_manager_->set_program_state(ProgramState::RUNNING);
  STRA_LOG(logger_, STRA_INFO,
           "Kline processing completed, initialization phase ended, program state set to RUNNING "
           "http_last_time:{}",
           http_last_time);
}

void strategy::on_http_rsp(i_strategy_ctx *ctx, http_rsp_field *field) {
  // Check if this is a kline request response
  if (field->req_type == req_type_type::REQ_GET_KLINE) {
    // Check if the request ID is in the current batch
    auto it = pending_request_ids_.find(field->request_id);
    if (it != pending_request_ids_.end()) {
      // Remove from pending set
      pending_request_ids_.erase(it);
      std::cout << "strategy http_rsp: " << field->request_id << std::endl;

      STRA_LOG(logger_, STRA_DEBUG,
               "Received kline request response, request_id: {}, "
               "remaining_requests: {}",
               field->request_id, pending_request_ids_.size());

      // If all requests in the current batch have been responded to, process
      // the next batch
      if (pending_request_ids_.size() == 0 && is_batch_processing_) {
        STRA_LOG(
            logger_, STRA_INFO,
            "All requests in current batch completed, processing next batch");
        process_next_batch();
      }
    }
  }
}
void strategy::check_flush(uint64_t current_time) {

  constexpr uint64_t update_threshold = 64 * 1e9;
  // 一定不满足一轮的条件了 应该更新标志位告诉读端可以读一下试试
  if (last_kline_close_time_ > 0 &&
      current_time > last_kline_close_time_ * 1e6 + update_threshold) {
    STRA_LOG(logger_, STRA_ERROR,
             "not_flush_shm last_flush_time:{} current_time:{}",
             last_kline_close_time_, current_time);
    kline_manager_->flush_to_shm();
    last_kline_close_time_ = last_kline_close_time_ + 60 * 1000;
    kline_manager_->set_last_update_time(last_kline_close_time_, true);
  }

  if (initialization_completed_) {
    std::vector<std::string> need_erase_contracts;
    for (const auto &pair : last_update_times_) {
      const std::string &instrument = pair.first;
      uint64_t last_update_time = pair.second;
      if (last_update_time != 0 &&
          current_time - last_update_time > update_threshold) {
        STRA_LOG(logger_, STRA_ERROR,
                 "ins {} hasn't been updated for {} seconds", instrument,
                 (current_time - last_update_time) / 1e9);
      }
      // 640s没更新了 认为这个合约有问题了 可以剔除掉 total_instruments_同步修改
      if (last_update_time != 0 &&
          current_time - last_update_time > 8 * update_threshold) {
        need_erase_contracts.push_back(instrument);
        STRA_LOG(logger_, STRA_ERROR,
                 "ins {} hasn't been updated for {} seconds need_erase",
                 instrument, (current_time - last_update_time) / 1e9);
      }
    }
    for (const auto &instrument : need_erase_contracts) {
      last_update_times_.erase(instrument);
      received_klines_.erase(instrument);
    }
    if (need_erase_contracts.size() > 0) {
      STRA_LOG(logger_, STRA_ERROR, "erase_contracts: {}",
               need_erase_contracts.size());
      total_instruments_ -= need_erase_contracts.size();
    }
  }
}

void strategy::on_trade_proxy_state(i_strategy_ctx *ctx,
                                    td_proxy_state_field *field) {}
} // namespace fast_trader_elite::strategy
