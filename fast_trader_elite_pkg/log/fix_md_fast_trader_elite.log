2025-06-13 22:27:22.453914904 INFO [3832087]  strategy_runner::init_md, md_cfg:[{"log_level":"debug","md_name":"bybit","md_index":0,"so_path":"../md_so_plugin/libbybit_md_proxy_plugin.so","exchange":"bybit"}]
2025-06-13 22:27:22.457687661 INFO [3832087]  strategy_runner::init_strategy loading strategy[fix_md_tool] from so_path[../strategy_plugin/libfix_md_tool.so]
2025-06-13 22:27:22.522837768 INFO [3832087]  strategy_runner::on_so_load load so_name[../strategy_plugin/libfix_md_tool.so], strategy_name[fix_md_tool]
2025-06-13 22:27:23.529126015 INFO [3832088]  ws_client_new::on_connect, ssl ec:[0] ec_msg:[Success]
2025-06-13 22:27:23.535688645 INFO [3832088]  ws_client_new::on_ssl_handshake, on_ssl_handshake ec:[0] ec_msg:[Success]
2025-06-13 22:27:26.027104787 INFO [3832116]  strategy_runner::init_md, md_cfg:[{"log_level":"debug","md_name":"bybit","md_index":0,"so_path":"../md_so_plugin/libbybit_md_proxy_plugin.so","exchange":"bybit"}]
2025-06-13 22:27:26.032134529 INFO [3832116]  strategy_runner::init_strategy loading strategy[fix_md_tool] from so_path[../strategy_plugin/libfix_md_tool.so]
2025-06-13 22:27:26.084470240 INFO [3832116]  strategy_runner::on_so_load load so_name[../strategy_plugin/libfix_md_tool.so], strategy_name[fix_md_tool]
2025-06-13 22:27:27.088966575 INFO [3832117]  ws_client_new::on_connect, ssl ec:[0] ec_msg:[Success]
2025-06-13 22:27:27.095532340 INFO [3832117]  ws_client_new::on_ssl_handshake, on_ssl_handshake ec:[0] ec_msg:[Success]
2025-06-13 22:29:12.880974371 INFO [3832561]  strategy_runner::init_md, md_cfg:[{"log_level":"debug","md_name":"bybit","md_index":0,"so_path":"../md_so_plugin/libbybit_md_proxy_plugin.so","exchange":"bybit"}]
2025-06-13 22:29:12.884662610 INFO [3832561]  strategy_runner::init_strategy loading strategy[fix_md_tool] from so_path[../strategy_plugin/libfix_md_tool.so]
2025-06-13 22:29:12.940017402 INFO [3832561]  strategy_runner::on_so_load load so_name[../strategy_plugin/libfix_md_tool.so], strategy_name[fix_md_tool]
2025-06-13 22:29:13.949888332 INFO [3832562]  ws_client_new::on_connect, ssl ec:[0] ec_msg:[Success]
2025-06-13 22:29:13.955228900 INFO [3832562]  ws_client_new::on_ssl_handshake, on_ssl_handshake ec:[0] ec_msg:[Success]
2025-06-13 22:29:16.400787965 INFO [3832590]  strategy_runner::init_md, md_cfg:[{"log_level":"debug","md_name":"bybit","md_index":0,"so_path":"../md_so_plugin/libbybit_md_proxy_plugin.so","exchange":"bybit"}]
2025-06-13 22:29:16.406294157 INFO [3832590]  strategy_runner::init_strategy loading strategy[fix_md_tool] from so_path[../strategy_plugin/libfix_md_tool.so]
2025-06-13 22:29:16.466086860 INFO [3832590]  strategy_runner::on_so_load load so_name[../strategy_plugin/libfix_md_tool.so], strategy_name[fix_md_tool]
2025-06-13 22:29:17.471428581 INFO [3832591]  ws_client_new::on_connect, ssl ec:[0] ec_msg:[Success]
2025-06-13 22:29:17.479350319 INFO [3832591]  ws_client_new::on_ssl_handshake, on_ssl_handshake ec:[0] ec_msg:[Success]
2025-06-13 22:29:25.992589863 INFO [3832658]  strategy_runner::init_md, md_cfg:[{"log_level":"debug","md_name":"bybit","md_index":0,"so_path":"../md_so_plugin/libbybit_md_proxy_plugin.so","exchange":"bybit"}]
2025-06-13 22:29:25.996362430 INFO [3832658]  strategy_runner::init_strategy loading strategy[fix_md_tool] from so_path[../strategy_plugin/libfix_md_tool.so]
2025-06-13 22:29:26.050694198 INFO [3832658]  strategy_runner::on_so_load load so_name[../strategy_plugin/libfix_md_tool.so], strategy_name[fix_md_tool]
2025-06-13 22:29:27.054824448 INFO [3832659]  ws_client_new::on_connect, ssl ec:[0] ec_msg:[Success]
2025-06-13 22:29:27.060694272 INFO [3832659]  ws_client_new::on_ssl_handshake, on_ssl_handshake ec:[0] ec_msg:[Success]
2025-06-13 22:29:35.509895042 INFO [3832709]  strategy_runner::init_md, md_cfg:[{"log_level":"debug","md_name":"bybit","md_index":0,"so_path":"../md_so_plugin/libbybit_md_proxy_plugin.so","exchange":"bybit"}]
2025-06-13 22:29:35.513739702 INFO [3832709]  strategy_runner::init_strategy loading strategy[fix_md_tool] from so_path[../strategy_plugin/libfix_md_tool.so]
2025-06-13 22:29:35.567375839 INFO [3832709]  strategy_runner::on_so_load load so_name[../strategy_plugin/libfix_md_tool.so], strategy_name[fix_md_tool]
2025-06-13 22:29:36.573408761 INFO [3832711]  ws_client_new::on_connect, ssl ec:[0] ec_msg:[Success]
2025-06-13 22:29:36.584591083 INFO [3832711]  ws_client_new::on_ssl_handshake, on_ssl_handshake ec:[0] ec_msg:[Success]
