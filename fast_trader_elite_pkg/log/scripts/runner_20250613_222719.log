2025-06-13 22:27:19,353 - INFO - 所有路径检查通过
2025-06-13 22:27:19,353 - INFO - === 第1步：运行auto_instrument.sh脚本 ===
2025-06-13 22:27:19,353 - INFO - 运行auto_instrument.sh脚本...
2025-06-13 22:27:19,353 - INFO - 命令: sh auto_instrument.sh
2025-06-13 22:27:19,353 - INFO - 工作目录: /home/<USER>/git/fast_trader_elite_pkg/auto_instrument
2025-06-13 22:27:19,353 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/auto_instrument_20250613_222719.log
2025-06-13 22:27:19,355 - INFO - auto_instrument.sh脚本已启动，PID: 3832067
2025-06-13 22:27:22,418 - INFO - auto_instrument.sh脚本执行成功
2025-06-13 22:27:22,419 - INFO - auto_instrument.sh脚本执行完成
2025-06-13 22:27:22,419 - INFO - === 第2步：运行fix_md_tools程序 ===
2025-06-13 22:27:22,419 - INFO - 运行fix_md_tools程序...
2025-06-13 22:27:22,419 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/fix_md.json
2025-06-13 22:27:22,419 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/fix_md_tools_20250613_222722.log
2025-06-13 22:27:22,422 - INFO - fix_md_tools程序已启动，PID: 3832087
2025-06-13 22:27:24,216 - INFO - fix_md_tools程序执行成功
2025-06-13 22:27:24,216 - INFO - fix_md_tools程序执行完成
2025-06-13 22:27:24,216 - INFO - === 第3步：运行kline_feed_shm_v1程序（常驻进程）===
2025-06-13 22:27:24,217 - INFO - 启动kline_feed_shm_v1程序...
2025-06-13 22:27:24,217 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/strategy_config/kline_feed_shm.json
2025-06-13 22:27:24,217 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/kline_feed_shm_20250613_222724.log
2025-06-13 22:27:24,219 - INFO - kline_feed_shm_v1程序已启动，PID: 3832097
2025-06-13 22:27:24,221 - WARNING - 共享内存K线更新超时: 1584.22秒 > 64秒
2025-06-13 22:27:24,221 - ERROR - 检测到共享内存更新超时，准备重启所有程序
2025-06-13 22:27:24,221 - INFO - 终止kline_feed_shm_v1进程
2025-06-13 22:27:24,221 - INFO - 所有程序已按顺序启动完成，kline_feed_shm_v1正在运行
2025-06-13 22:27:24,221 - INFO - 共享内存监控已启用，超时阈值: 64秒
2025-06-13 22:27:24,221 - INFO - 按Ctrl+C退出
2025-06-13 22:27:24,222 - INFO - 开始重启所有程序...
2025-06-13 22:27:24,222 - INFO - === 重启程序：第1步 - 运行auto_instrument.sh ===
2025-06-13 22:27:24,222 - INFO - 运行auto_instrument.sh脚本...
2025-06-13 22:27:24,222 - INFO - 命令: sh auto_instrument.sh
2025-06-13 22:27:24,222 - INFO - 工作目录: /home/<USER>/git/fast_trader_elite_pkg/auto_instrument
2025-06-13 22:27:24,222 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/auto_instrument_20250613_222724.log
2025-06-13 22:27:24,224 - INFO - auto_instrument.sh脚本已启动，PID: 3832099
2025-06-13 22:27:25,993 - INFO - auto_instrument.sh脚本执行成功
2025-06-13 22:27:25,993 - INFO - === 重启程序：第2步 - 运行fix_md_tools ===
2025-06-13 22:27:25,993 - INFO - 运行fix_md_tools程序...
2025-06-13 22:27:25,993 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/fix_md.json
2025-06-13 22:27:25,994 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/fix_md_tools_20250613_222725.log
2025-06-13 22:27:25,995 - INFO - fix_md_tools程序已启动，PID: 3832116
2025-06-13 22:27:27,742 - INFO - fix_md_tools程序执行成功
2025-06-13 22:27:27,742 - INFO - === 重启程序：第3步 - 运行kline_feed_shm_v1 ===
2025-06-13 22:27:27,742 - INFO - 启动kline_feed_shm_v1程序...
2025-06-13 22:27:27,742 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/strategy_config/kline_feed_shm.json
2025-06-13 22:27:27,742 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/kline_feed_shm_20250613_222727.log
2025-06-13 22:27:27,744 - INFO - kline_feed_shm_v1程序已启动，PID: 3832136
2025-06-13 22:27:27,744 - INFO - 所有程序重启完成
2025-06-13 22:27:32,749 - WARNING - kline_feed_shm_v1程序意外退出，退出码: -6
2025-06-13 22:27:37,754 - WARNING - kline_feed_shm_v1程序意外退出，退出码: -6
2025-06-13 22:27:39,635 - INFO - 收到信号 2，正在退出...
2025-06-13 22:27:39,635 - INFO - 正在停止所有进程...
