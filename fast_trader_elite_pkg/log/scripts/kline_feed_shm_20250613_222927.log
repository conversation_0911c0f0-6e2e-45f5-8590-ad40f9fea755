exchange:bybit instrument_idx_config_path:../config/instrument_config/bybit_ins_map.json instrument_info_config_path:../config/instrument_config/bybit_future_infos.json
../log/shm_kline_feed_fast_trader_elite.log
[INFO] [2025-06-13 22:29:27.742] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_0] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_0.log
[INFO] [2025-06-13 22:29:27.743] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_0] bybit_md_proxy init
add_adapter md_id:0
[INFO] [2025-06-13 22:29:27.744] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_1] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_1.log
[INFO] [2025-06-13 22:29:27.744] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_1] bybit_md_proxy init
add_adapter md_id:1
[INFO] [2025-06-13 22:29:27.745] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_2] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_2.log
[INFO] [2025-06-13 22:29:27.745] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_2] bybit_md_proxy init
add_adapter md_id:2
[INFO] [2025-06-13 22:29:27.745] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_3] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_3.log
[INFO] [2025-06-13 22:29:27.746] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_3] bybit_md_proxy init
add_adapter md_id:3
[INFO] [2025-06-13 22:29:27.746] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_4] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_4.log
[INFO] [2025-06-13 22:29:27.746] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_4] bybit_md_proxy init
add_adapter md_id:4
[INFO] [2025-06-13 22:29:27.747] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_5] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_5.log
[INFO] [2025-06-13 22:29:27.747] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_5] bybit_md_proxy init
add_adapter md_id:5
[INFO] [2025-06-13 22:29:27.747] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_6] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_6.log
[INFO] [2025-06-13 22:29:27.748] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_6] bybit_md_proxy init
add_adapter md_id:6
[INFO] [2025-06-13 22:29:27.748] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_7] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_7.log
[INFO] [2025-06-13 22:29:27.748] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_7] bybit_md_proxy init
add_adapter md_id:7
[INFO] [2025-06-13 22:29:27.749] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_8] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_8.log
[INFO] [2025-06-13 22:29:27.749] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_8] bybit_md_proxy init
add_adapter md_id:8
[INFO] [2025-06-13 22:29:27.749] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_9] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_9.log
[INFO] [2025-06-13 22:29:27.750] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_9] bybit_md_proxy init
add_adapter md_id:9
my_strategy_so::create
[INFO] [2025-06-13 22:29:27.803] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_0] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_0] bybit_md_proxy connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_1] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_1] bybit_md_proxy connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_2] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_2] bybit_md_proxy connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_3] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_3] bybit_md_proxy connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_4] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_4] bybit_md_proxy connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_5] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_5] bybit_md_proxy connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_6] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_6] bybit_md_proxy connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_7] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_7] bybit_md_proxy connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_8] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_8] bybit_md_proxy connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_9] bybit_md_proxy do_ws_connect
async_connect
[INFO] [2025-06-13 22:29:27.804] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_9] bybit_md_proxy connect
register_md_inner_spi
register_md_inner_spi
register_md_inner_spi
register_md_inner_spi
register_md_inner_spi
register_md_inner_spi
register_md_inner_spi
register_md_inner_spi
register_md_inner_spi
register_md_inner_spi
on start, strategy_name: kline_feed_shm
parse_config:{"kline_cnt":13000,"save_path":"/home/<USER>/git/fast_trader_elite_pkg/main/"}
data_->save_path:/home/<USER>/git/fast_trader_elite_pkg/main/
shm_path_:/home/<USER>/git/fast_trader_elite_pkg/main//kline_data.shm
shm_path_:/home/<USER>/git/fast_trader_elite_pkg/main//kline_data.shm
post force_close
force_close
post reset
post reset
post force_close
force_close
post force_close
force_close
post reset
post reset
post force_close
force_close
post force_close
force_close
post force_close
force_close
post reset
post force_close
force_close
post reset
post force_close
force_close
post reset
post force_close
force_close
post reset
post reset
post force_close
force_close
post reset
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_0] bybit_md_proxy subscribe: received 53 codes, new codes: 53, total codes: 53 force:false
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_1] bybit_md_proxy subscribe: received 53 codes, new codes: 53, total codes: 53 force:false
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_2] bybit_md_proxy subscribe: received 53 codes, new codes: 53, total codes: 53 force:false
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_3] bybit_md_proxy subscribe: received 53 codes, new codes: 53, total codes: 53 force:false
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_4] bybit_md_proxy subscribe: received 53 codes, new codes: 53, total codes: 53 force:false
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_5] bybit_md_proxy subscribe: received 53 codes, new codes: 53, total codes: 53 force:false
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_6] bybit_md_proxy subscribe: received 53 codes, new codes: 53, total codes: 53 force:false
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_7] bybit_md_proxy subscribe: received 53 codes, new codes: 53, total codes: 53 force:false
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_8] bybit_md_proxy subscribe: received 52 codes, new codes: 52, total codes: 52 force:false
[INFO] [2025-06-13 22:29:27.846] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_9] bybit_md_proxy subscribe: received 52 codes, new codes: 52, total codes: 52 force:false
strategy request_id: 1000 1000XUSDT
fetch_klines, request_id: 1000 instrument: 1000XUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1001 MELANIAUSDT
fetch_klines, request_id: 1001 instrument: MELANIAUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1002 UXLINKUSDT
fetch_klines, request_id: 1002 instrument: UXLINKUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1003 MANAUSDT
fetch_klines, request_id: 1003 instrument: MANAUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1004 HIVEUSDT
fetch_klines, request_id: 1004 instrument: HIVEUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1005 YFIUSDT
fetch_klines, request_id: 1005 instrument: YFIUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1006 ALEOUSDT
fetch_klines, request_id: 1006 instrument: ALEOUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1007 MDTUSDT
fetch_klines, request_id: 1007 instrument: MDTUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1008 RVNUSDT
fetch_klines, request_id: 1008 instrument: RVNUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1009 GTCUSDT
fetch_klines, request_id: 1009 instrument: GTCUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1010 BADGERUSDT
fetch_klines, request_id: 1010 instrument: BADGERUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1011 SUSDT
fetch_klines, request_id: 1011 instrument: SUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1012 MOCAUSDT
fetch_klines, request_id: 1012 instrument: MOCAUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1013 SOONUSDT
fetch_klines, request_id: 1013 instrument: SOONUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1014 JUSDT
fetch_klines, request_id: 1014 instrument: JUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1015 FORMUSDT
fetch_klines, request_id: 1015 instrument: FORMUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1016 MEUSDT
fetch_klines, request_id: 1016 instrument: MEUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1017 DARKUSDT
fetch_klines, request_id: 1017 instrument: DARKUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1018 GRASSUSDT
fetch_klines, request_id: 1018 instrument: GRASSUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
strategy request_id: 1019 ZETAUSDT
fetch_klines, request_id: 1019 instrument: ZETAUSDT start_time: ************* end_time: 1749824967847 kline_cnt: 1000
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
post async_connect_inner
do reset
async_resolve
post async_connect_inner
do reset
async_resolve
post async_connect_inner
do reset
async_resolve
post async_connect_inner
do reset
async_resolve
post async_connect_inner
do reset
async_resolve
post async_connect_inner
do reset
async_resolve
post async_connect_inner
do reset
async_resolve
post async_connect_inner
do reset
async_resolve
post async_connect_inner
do reset
async_resolve
post async_connect_inner
do reset
async_resolve
on_resolve system:0 ec_msg:Success
on_resolve system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_resolve system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_resolve system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_resolve system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_resolve system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_resolve system:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_resolve system:0 ec_msg:Success
on_resolve system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_resolve system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.922] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_3] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.922] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_3] bybit_md_proxy subscribe: received 53 codes, new codes: 0, total codes: 53 force:false
[INFO] [2025-06-13 22:29:28.922] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_3] bybit_md_proxy: full subscription with 53 codes
do write:1
[INFO] [2025-06-13 22:29:28.922] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_3] bybit_md_proxy do_sub: {"args":["kline.1.REQUSDT","kline.1.GASUSDT","kline.1.SANDUSDT","kline.1.VELODROMEUSDT","kline.1.C98USDT","kline.1.JSTUSDT","kline.1.KERNELUSDT","kline.1.HPOS10IUSDT","kline.1.TUSDT","kline.1.API3USDT","kline.1.TRUUSDT","kline.1.SIGNUSDT","kline.1.FIDAUSDT","kline.1.COMPUSDT","kline.1.BANDUSDT","kline.1.BANUSDT","kline.1.CUDISUSDT","kline.1.ILVUSDT","kline.1.DOGEUSDT","kline.1.SIRENUSDT","kline.1.ROAMUSDT","kline.1.PIXELUSDT","kline.1.RAYDIUMUSDT","kline.1.HBARUSDT","kline.1.SNXUSDT","kline.1.AVLUSDT","kline.1.ACEUSDT","kline.1.LUNA2USDT","kline.1.XTERUSDT","kline.1.BOMEUSDT","kline.1.ELXUSDT","kline.1.REXUSDT","kline.1.TAOUSDT","kline.1.EPICUSDT","kline.1.XRPUSDT","kline.1.XAUTUSDT","kline.1.FUELUSDT","kline.1.10000WHYUSDT","kline.1.GOATUSDT","kline.1.SUIUSDT","kline.1.SOONUSDT","kline.1.JOEUSDT","kline.1.SFPUSDT","kline.1.BOBAUSDT","kline.1.IOTAUSDT","kline.1.BELUSDT","kline.1.XTZUSDT","kline.1.MANAUSDT","kline.1.FIOUSDT","kline.1.NCUSDT","kline.1.BANKUSDT","kline.1.SPELLUSDT","kline.1.10000WENUSDT"],"op":"subscribe","req_id":"0"}
[INFO] [2025-06-13 22:29:28.922] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_3] bybit_md_proxy send msg ec: 0 ec_msg: Success
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.926] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_2] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.926] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_2] bybit_md_proxy subscribe: received 53 codes, new codes: 0, total codes: 53 force:false
[INFO] [2025-06-13 22:29:28.926] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_2] bybit_md_proxy: full subscription with 53 codes
do write:1
[INFO] [2025-06-13 22:29:28.927] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_2] bybit_md_proxy do_sub: {"args":["kline.1.JTOUSDT","kline.1.BCHUSDT","kline.1.LISTAUSDT","kline.1.ZENTUSDT","kline.1.NEOUSDT","kline.1.RPLUSDT","kline.1.MICHIUSDT","kline.1.SWEATUSDT","kline.1.FUSDT","kline.1.HIFIUSDT","kline.1.HIGHUSDT","kline.1.OMUSDT","kline.1.MKRUSDT","kline.1.ARBUSDT","kline.1.PUFFERUSDT","kline.1.TRXUSDT","kline.1.JUPUSDT","kline.1.CRVUSDT","kline.1.TSTBSCUSDT","kline.1.CFXUSDT","kline.1.OMNIUSDT","kline.1.ICXUSDT","kline.1.BROCCOLIUSDT","kline.1.A8USDT","kline.1.PLUMEUSDT","kline.1.KAVAUSDT","kline.1.SPECUSDT","kline.1.10000COQUSDT","kline.1.HAEDALUSDT","kline.1.BLURUSDT","kline.1.XCNUSDT","kline.1.ENSUSDT","kline.1.SXPUSDT","kline.1.KAIAUSDT","kline.1.DOGUSDT","kline.1.UXLINKUSDT","kline.1.HEIUSDT","kline.1.MUBARAKUSDT","kline.1.VANRYUSDT","kline.1.VETUSDT","kline.1.RAREUSDT","kline.1.IOTXUSDT","kline.1.SOLAYERUSDT","kline.1.BANANAUSDT","kline.1.HYPEUSDT","kline.1.MOCAUSDT","kline.1.1000TOSHIUSDT","kline.1.CPOOLUSDT","kline.1.PRCLUSDT","kline.1.BSWUSDT","kline.1.10000SATSUSDT","kline.1.USUALUSDT","kline.1.USTCUSDT"],"op":"subscribe","req_id":"0"}
[INFO] [2025-06-13 22:29:28.927] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_2] bybit_md_proxy send msg ec: 0 ec_msg: Success
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.940] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_8] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.940] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_8] bybit_md_proxy subscribe: received 52 codes, new codes: 0, total codes: 52 force:false
[INFO] [2025-06-13 22:29:28.940] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_8] bybit_md_proxy: full subscription with 52 codes
do write:1
[INFO] [2025-06-13 22:29:28.940] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_8] bybit_md_proxy do_sub: {"args":["kline.1.MVLUSDT","kline.1.ONTUSDT","kline.1.GUSDT","kline.1.TNSRUSDT","kline.1.ZBCNUSDT","kline.1.PEOPLEUSDT","kline.1.REZUSDT","kline.1.MASAUSDT","kline.1.ARKUSDT","kline.1.HIPPOUSDT","kline.1.BABYUSDT","kline.1.RADUSDT","kline.1.VELOUSDT","kline.1.HOMEUSDT","kline.1.PAXGUSDT","kline.1.SWELLUSDT","kline.1.KASUSDT","kline.1.AIOZUSDT","kline.1.AUDIOUSDT","kline.1.OXTUSDT","kline.1.CROUSDT","kline.1.KMNOUSDT","kline.1.UNIUSDT","kline.1.TAIUSDT","kline.1.CHZUSDT","kline.1.SKYAIUSDT","kline.1.CTSIUSDT","kline.1.COREUSDT","kline.1.1000000PEIPEIUSDT","kline.1.ALPHAUSDT","kline.1.REDUSDT","kline.1.MAJORUSDT","kline.1.IOUSDT","kline.1.NTRNUSDT","kline.1.QUICKUSDT","kline.1.KOMAUSDT","kline.1.GRASSUSDT","kline.1.AEVOUSDT","kline.1.ICPUSDT","kline.1.1000000CHEEMSUSDT","kline.1.L3USDT","kline.1.RVNUSDT","kline.1.COTIUSDT","kline.1.YGGUSDT","kline.1.ARCUSDT","kline.1.RLCUSDT","kline.1.ENJUSDT","kline.1.NEIROETHUSDT","kline.1.DOGSUSDT","kline.1.GMXUSDT","kline.1.DEXEUSDT","kline.1.MOVEUSDT"],"op":"subscribe","req_id":"0"}
[INFO] [2025-06-13 22:29:28.940] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_8] bybit_md_proxy send msg ec: 0 ec_msg: Success
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.950] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_7] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.950] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_7] bybit_md_proxy subscribe: received 53 codes, new codes: 0, total codes: 53 force:false
[INFO] [2025-06-13 22:29:28.950] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_7] bybit_md_proxy: full subscription with 53 codes
do write:1
[INFO] [2025-06-13 22:29:28.950] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_7] bybit_md_proxy do_sub: {"args":["kline.1.COOKUSDT","kline.1.PUMPBTCUSDT","kline.1.WAVESUSDT","kline.1.XVGUSDT","kline.1.BBUSDT","kline.1.FXSUSDT","kline.1.ZECUSDT","kline.1.KDAUSDT","kline.1.MBLUSDT","kline.1.SOLUSDT","kline.1.PERPUSDT","kline.1.PORTALUSDT","kline.1.PARTIUSDT","kline.1.NMRUSDT","kline.1.RSS3USDT","kline.1.GLMUSDT","kline.1.BMTUSDT","kline.1.VTHOUSDT","kline.1.DYDXUSDT","kline.1.SUNDOGUSDT","kline.1.SCRTUSDT","kline.1.XMRUSDT","kline.1.PYTHUSDT","kline.1.MTLUSDT","kline.1.MINAUSDT","kline.1.ZRXUSDT","kline.1.STORJUSDT","kline.1.ALICEUSDT","kline.1.QIUSDT","kline.1.1000LUNCUSDT","kline.1.OGUSDT","kline.1.DYMUSDT","kline.1.ONGUSDT","kline.1.NILUSDT","kline.1.ALGOUSDT","kline.1.CHRUSDT","kline.1.DARKUSDT","kline.1.QNTUSDT","kline.1.IOSTUSDT","kline.1.MYROUSDT","kline.1.LRCUSDT","kline.1.OGNUSDT","kline.1.NSUSDT","kline.1.MDTUSDT","kline.1.SKATEUSDT","kline.1.ENAUSDT","kline.1.CYBERUSDT","kline.1.1INCHUSDT","kline.1.STOUSDT","kline.1.CHILLGUYUSDT","kline.1.10000ELONUSDT","kline.1.GNOUSDT","kline.1.1000XECUSDT"],"op":"subscribe","req_id":"0"}
[INFO] [2025-06-13 22:29:28.951] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_7] bybit_md_proxy send msg ec: 0 ec_msg: Success
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.955] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_0] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.955] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_0] bybit_md_proxy subscribe: received 53 codes, new codes: 0, total codes: 53 force:false
[INFO] [2025-06-13 22:29:28.955] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_0] bybit_md_proxy: full subscription with 53 codes
[INFO] [2025-06-13 22:29:28.955] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_0] bybit_md_proxy do_sub: {"args":["kline.1.SERAPHUSDT","kline.1.SNTUSDT","kline.1.CVCUSDT","kline.1.B2USDT","kline.1.BICOUSDT","kline.1.UMAUSDT","kline.1.CARVUSDT","kline.1.CVXUSDT","kline.1.MERLUSDT","kline.1.APTUSDT","kline.1.HYPERUSDT","kline.1.AUCTIONUSDT","kline.1.CLANKERUSDT","kline.1.INJUSDT","kline.1.WALUSDT","kline.1.ORCAUSDT","kline.1.MOVRUSDT","kline.1.DBRUSDT","kline.1.OBTUSDT","kline.1.NFPUSDT","kline.1.ZENUSDT","kline.1.TAIKOUSDT","kline.1.LOOKSUSDT","kline.1.PIPPINUSDT","kline.1.BANANAS31USDT","kline.1.ETCUSDT","kline.1.BTCUSDT","kline.1.IPUSDT","kline.1.AERGOUSDT","kline.1.VIRTUALUSDT","kline.1.SCAUSDT","kline.1.RUNEUSDT","kline.1.TWTUSDT","kline.1.GORKUSDT","kline.1.1000000BABYDOGEUSDT","kline.1.ALTUSDT","kline.1.COWUSDT","kline.1.HMSTRUSDT","kline.1.BRETTUSDT","kline.1.SYNUSDT","kline.1.RENDERUSDT","kline.1.FARTCOINUSDT","kline.1.BADGERUSDT","kline.1.1000XUSDT","kline.1.ORDERUSDT","kline.1.VANAUSDT","kline.1.MOODENGUSDT","kline.1.ARPAUSDT","kline.1.ONEUSDT","kline.1.BRUSDT","kline.1.WUSDT","kline.1.LPTUSDT","kline.1.IDUSDT"],"op":"subscribe","req_id":"0"}
do write:1
[INFO] [2025-06-13 22:29:28.955] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_0] bybit_md_proxy send msg ec: 0 ec_msg: Success
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.958] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_5] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.958] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_5] bybit_md_proxy subscribe: received 53 codes, new codes: 0, total codes: 53 force:false
[INFO] [2025-06-13 22:29:28.958] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_5] bybit_md_proxy: full subscription with 53 codes
do write:1[INFO] [2025-06-13 22:29:28.959] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_5] bybit_md_proxy do_sub: {"args":["kline.1.METISUSDT","kline.1.SONICUSDT","kline.1.YFIUSDT","kline.1.SSVUSDT","kline.1.CAKEUSDT","kline.1.AVAUSDT","kline.1.FBUSDT","kline.1.SUSHIUSDT","kline.1.HOTUSDT","kline.1.ARKMUSDT","kline.1.ALCHUSDT","kline.1.BSVUSDT","kline.1.MAVUSDT","kline.1.CGPTUSDT","kline.1.ALUUSDT","kline.1.SENDUSDT","kline.1.FHEUSDT","kline.1.MAVIAUSDT","kline.1.SUNUSDT","kline.1.FWOGUSDT","kline.1.VVVUSDT","kline.1.MBOXUSDT","kline.1.WOOUSDT","kline.1.JASMYUSDT","kline.1.PUNDIXUSDT","kline.1.COOKIEUSDT","kline.1.ROSEUSDT","kline.1.SOLVUSDT","kline.1.ETHBTCUSDT","kline.1.TONUSDT","kline.1.DOTUSDT","kline.1.USDCUSDT","kline.1.BALUSDT","kline.1.STEEMUSDT","kline.1.SUPERUSDT","kline.1.POPCATUSDT","kline.1.HFTUSDT","kline.1.SEIUSDT","kline.1.FORMUSDT","kline.1.ZKUSDT","kline.1.SCUSDT","kline.1.LQTYUSDT","kline.1.LUMIAUSDT","kline.1.ZORAUSDT","kline.1.WLDUSDT","kline.1.PEAQUSDT","kline.1.BDXNUSDT","kline.1.LSKUSDT","kline.1.AEROUSDT","kline.1.ZEUSUSDT","kline.1.QTUMUSDT","kline.1.COSUSDT","kline.1.AXLUSDT"],"op":"subscribe","req_id":"0"}

[INFO] [2025-06-13 22:29:28.959] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_5] bybit_md_proxy send msg ec: 0 ec_msg: Success
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.962] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_4] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.962] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_4] bybit_md_proxy subscribe: received 53 codes, new codes: 0, total codes: 53 force:false
[INFO] [2025-06-13 22:29:28.962] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_4] bybit_md_proxy: full subscription with 53 codes
[INFO] [2025-06-13 22:29:28.963] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_4] bybit_md_proxy do_sub: {"args":["kline.1.OPUSDT","kline.1.KNCUSDT","kline.1.BNBUSDT","kline.1.MORPHOUSDT","kline.1.EGLDUSDT","kline.1.BIOUSDT","kline.1.IDEXUSDT","kline.1.1000PEPEUSDT","kline.1.PHBUSDT","kline.1.POWRUSDT","kline.1.MAGICUSDT","kline.1.AIXBTUSDT","kline.1.1000BONKUSDT","kline.1.TRBUSDT","kline.1.ONDOUSDT","kline.1.POLUSDT","kline.1.SYRUPUSDT","kline.1.FLUXUSDT","kline.1.RSRUSDT","kline.1.1000FLOKIUSDT","kline.1.BERAUSDT","kline.1.1000NEIROCTOUSDT","kline.1.BATUSDT","kline.1.XDCUSDT","kline.1.GMTUSDT","kline.1.ZILUSDT","kline.1.HOOKUSDT","kline.1.B3USDT","kline.1.ZEREBROUSDT","kline.1.BEAMUSDT","kline.1.DENTUSDT","kline.1.RONINUSDT","kline.1.JUSDT","kline.1.AGIUSDT","kline.1.TUTUSDT","kline.1.1000RATSUSDT","kline.1.KAITOUSDT","kline.1.ETHUSDT","kline.1.FORTHUSDT","kline.1.SOPHUSDT","kline.1.ASTRUSDT","kline.1.SHELLUSDT","kline.1.PENGUUSDT","kline.1.HIVEUSDT","kline.1.BIGTIMEUSDT","kline.1.SYSUSDT","kline.1.STGUSDT","kline.1.EPTUSDT","kline.1.NKNUSDT","kline.1.ORBSUSDT","kline.1.FTNUSDT","kline.1.APEUSDT","kline.1.TIAUSDT"],"op":"subscribe","req_id":"0"}
do write:1
[INFO] [2025-06-13 22:29:28.963] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_4] bybit_md_proxy send msg ec: 0 ec_msg: Success
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.964] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_6] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.964] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_6] bybit_md_proxy subscribe: received 53 codes, new codes: 0, total codes: 53 force:false
[INFO] [2025-06-13 22:29:28.964] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_6] bybit_md_proxy: full subscription with 53 codes
[INFO] [2025-06-13 22:29:28.964] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_6] bybit_md_proxy do_sub: {"args":["kline.1.AGLDUSDT","kline.1.1000000MOGUSDT","kline.1.GLMRUSDT","kline.1.1000TURBOUSDT","kline.1.ADAUSDT","kline.1.BLASTUSDT","kline.1.CTKUSDT","kline.1.HUMAUSDT","kline.1.ATAUSDT","kline.1.STXUSDT","kline.1.CHESSUSDT","kline.1.DUSKUSDT","kline.1.ANIMEUSDT","kline.1.SAFEUSDT","kline.1.CATIUSDT","kline.1.1000CATSUSDT","kline.1.SQDUSDT","kline.1.LINKUSDT","kline.1.GUNUSDT","kline.1.BAKEUSDT","kline.1.ALEOUSDT","kline.1.DRIFTUSDT","kline.1.PROMUSDT","kline.1.CELRUSDT","kline.1.MOBILEUSDT","kline.1.ETHFIUSDT","kline.1.SCRUSDT","kline.1.OBOLUSDT","kline.1.FLOCKUSDT","kline.1.ACHUSDT","kline.1.XEMUSDT","kline.1.MLNUSDT","kline.1.LAUNCHCOINUSDT","kline.1.PRIMEUSDT","kline.1.RIFUSDT","kline.1.DUCKUSDT","kline.1.RESOLVUSDT","kline.1.1000CATUSDT","kline.1.PONKEUSDT","kline.1.ACXUSDT","kline.1.EIGENUSDT","kline.1.OLUSDT","kline.1.PENDLEUSDT","kline.1.RDNTUSDT","kline.1.MEUSDT","kline.1.SHIB1000USDT","kline.1.XNOUSDT","kline.1.THEUSDT","kline.1.MASKUSDT","kline.1.IMXUSDT","kline.1.1000BTTUSDT","kline.1.MANTAUSDT","kline.1.LEVERUSDT"],"op":"subscribe","req_id":"0"}
do write:1
[INFO] [2025-06-13 22:29:28.965] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_6] bybit_md_proxy send msg ec: 0 ec_msg: Success
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.966] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_1] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.966] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_1] bybit_md_proxy subscribe: received 53 codes, new codes: 0, total codes: 53 force:false
[INFO] [2025-06-13 22:29:28.966] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_1] bybit_md_proxy: full subscription with 53 codes
[INFO] [2025-06-13 22:29:28.966] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_1] bybit_md_proxy do_sub: {"args":["kline.1.AWEUSDT","kline.1.AIUSDT","kline.1.SLPUSDT","kline.1.RFCUSDT","kline.1.USDEUSDT","kline.1.SWARMSUSDT","kline.1.SDUSDT","kline.1.DOODUSDT","kline.1.AVAAIUSDT","kline.1.MELANIAUSDT","kline.1.SKLUSDT","kline.1.ORDIUSDT","kline.1.FLRUSDT","kline.1.OSMOUSDT","kline.1.KSMUSDT","kline.1.CETUSUSDT","kline.1.SPXUSDT","kline.1.THETAUSDT","kline.1.VINEUSDT","kline.1.10000QUBICUSDT","kline.1.PRAIUSDT","kline.1.VRUSDT","kline.1.NOTUSDT","kline.1.DEEPUSDT","kline.1.CTCUSDT","kline.1.POLYXUSDT","kline.1.EDUUSDT","kline.1.GALAUSDT","kline.1.XVSUSDT","kline.1.MEWUSDT","kline.1.STRKUSDT","kline.1.AVAXUSDT","kline.1.10000LADYSUSDT","kline.1.NXPCUSDT","kline.1.INITUSDT","kline.1.AUSDT","kline.1.FLOWUSDT","kline.1.XCHUSDT","kline.1.AKTUSDT","kline.1.SLERFUSDT","kline.1.XRDUSDT","kline.1.SUSDT","kline.1.SAROSUSDT","kline.1.SLFUSDT","kline.1.LTCUSDT","kline.1.GODSUSDT","kline.1.XLMUSDT","kline.1.TRUMPUSDT","kline.1.DGBUSDT","kline.1.AXSUSDT","kline.1.AGTUSDT","kline.1.BNTUSDT","kline.1.GIGAUSDT"],"op":"subscribe","req_id":"0"}
do write:1
[INFO] [2025-06-13 22:29:28.966] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_1] bybit_md_proxy send msg ec: 0 ec_msg: Success
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:28.968] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_9] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:28.968] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_9] bybit_md_proxy subscribe: received 52 codes, new codes: 0, total codes: 52 force:false
[INFO] [2025-06-13 22:29:28.968] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_9] bybit_md_proxy: full subscription with 52 codes
do write:1
[INFO] [2025-06-13 22:29:28.968] [bybit_md_proxy.cc:206] [do_sub] [bybit_md_proxy_9] bybit_md_proxy do_sub: {"args":["kline.1.CLOUDUSDT","kline.1.ETHWUSDT","kline.1.MNTUSDT","kline.1.HNTUSDT","kline.1.CELOUSDT","kline.1.AAVEUSDT","kline.1.AVAILUSDT","kline.1.LAUSDT","kline.1.PYRUSDT","kline.1.PROMPTUSDT","kline.1.JELLYJELLYUSDT","kline.1.SAGAUSDT","kline.1.1000APUUSDT","kline.1.SOLOUSDT","kline.1.GTCUSDT","kline.1.GRTUSDT","kline.1.ZRCUSDT","kline.1.ZKJUSDT","kline.1.CKBUSDT","kline.1.FILUSDT","kline.1.XIONUSDT","kline.1.WAXPUSDT","kline.1.TOKENUSDT","kline.1.ARUSDT","kline.1.FLMUSDT","kline.1.MYRIAUSDT","kline.1.VOXELUSDT","kline.1.PHAUSDT","kline.1.PNUTUSDT","kline.1.MEMEUSDT","kline.1.ANKRUSDT","kline.1.DASHUSDT","kline.1.ZROUSDT","kline.1.GPSUSDT","kline.1.MILKUSDT","kline.1.ATHUSDT","kline.1.ACTUSDT","kline.1.LDOUSDT","kline.1.WCTUSDT","kline.1.DEGENUSDT","kline.1.BUSDT","kline.1.SXTUSDT","kline.1.WIFUSDT","kline.1.DODOUSDT","kline.1.NEARUSDT","kline.1.ZETAUSDT","kline.1.GRIFFAINUSDT","kline.1.ATOMUSDT","kline.1.TLMUSDT","kline.1.AI16ZUSDT","kline.1.VICUSDT","kline.1.XAIUSDT"],"op":"subscribe","req_id":"0"}
[INFO] [2025-06-13 22:29:28.969] [bybit_md_proxy.cc:310] [on_sent] [bybit_md_proxy_9] bybit_md_proxy send msg ec: 0 ec_msg: Success
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824967847 (last batch size: 30)
strategy http_rsp: 1000
request completed, request_id: 1000 instrument:1000XUSDT
strategy http_rsp: 1001
request completed, request_id: 1001 instrument:MELANIAUSDT
strategy http_rsp: 1002
request completed, request_id: 1002 instrument:UXLINKUSDT
strategy http_rsp: 1003
request completed, request_id: 1003 instrument:MANAUSDT
strategy http_rsp: 1004
request completed, request_id: 1004 instrument:HIVEUSDT
strategy http_rsp: 1005
request completed, request_id: 1005 instrument:YFIUSDT
strategy http_rsp: 1006
request completed, request_id: 1006 instrument:ALEOUSDT
strategy http_rsp: 1007
request completed, request_id: 1007 instrument:MDTUSDT
strategy http_rsp: 1008
request completed, request_id: 1008 instrument:RVNUSDT
strategy http_rsp: 1009
request completed, request_id: 1009 instrument:GTCUSDT
strategy http_rsp: 1010
request completed, request_id: 1010 instrument:BADGERUSDT
strategy http_rsp: 1011
request completed, request_id: 1011 instrument:SUSDT
strategy http_rsp: 1012
request completed, request_id: 1012 instrument:MOCAUSDT
strategy http_rsp: 1013
request completed, request_id: 1013 instrument:SOONUSDT
strategy http_rsp: 1014
request completed, request_id: 1014 instrument:JUSDT
strategy http_rsp: 1015
request completed, request_id: 1015 instrument:FORMUSDT
strategy http_rsp: 1016
request completed, request_id: 1016 instrument:MEUSDT
strategy http_rsp: 1017
request completed, request_id: 1017 instrument:DARKUSDT
strategy http_rsp: 1018
request completed, request_id: 1018 instrument:GRASSUSDT
strategy http_rsp: 1019
strategy request_id: 1020 AERGOUSDT
fetch_klines, request_id: 1020 instrument: AERGOUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1021 SLERFUSDT
fetch_klines, request_id: 1021 instrument: SLERFUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1022 1000TOSHIUSDT
fetch_klines, request_id: 1022 instrument: 1000TOSHIUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1023 XTZUSDT
fetch_klines, request_id: 1023 instrument: XTZUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1024 SYSUSDT
fetch_klines, request_id: 1024 instrument: SYSUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1025 SUPERUSDT
fetch_klines, request_id: 1025 instrument: SUPERUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1026 FLOCKUSDT
fetch_klines, request_id: 1026 instrument: FLOCKUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1027 PUMPBTCUSDT
fetch_klines, request_id: 1027 instrument: PUMPBTCUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1028 AEVOUSDT
fetch_klines, request_id: 1028 instrument: AEVOUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1029 GRIFFAINUSDT
fetch_klines, request_id: 1029 instrument: GRIFFAINUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1030 VIRTUALUSDT
fetch_klines, request_id: 1030 instrument: VIRTUALUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1031 XCHUSDT
fetch_klines, request_id: 1031 instrument: XCHUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1032 10000SATSUSDT
fetch_klines, request_id: 1032 instrument: 10000SATSUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1033 BANKUSDT
fetch_klines, request_id: 1033 instrument: BANKUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1034 APEUSDT
fetch_klines, request_id: 1034 instrument: APEUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1035 FHEUSDT
fetch_klines, request_id: 1035 instrument: FHEUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1036 HUMAUSDT
fetch_klines, request_id: 1036 instrument: HUMAUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1037 OGNUSDT
fetch_klines, request_id: 1037 instrument: OGNUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1038 DOGSUSDT
fetch_klines, request_id: 1038 instrument: DOGSUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1039 ANKRUSDT
fetch_klines, request_id: 1039 instrument: ANKRUSDT start_time: ************* end_time: ************* kline_cnt: 1000
request completed, request_id: 1019 instrument:ZETAUSDT
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
strategy http_rsp: 1020
request completed, request_id: 1020 instrument:AERGOUSDT
strategy http_rsp: 1021
request completed, request_id: 1021 instrument:SLERFUSDT
strategy http_rsp: 1022
request completed, request_id: 1022 instrument:1000TOSHIUSDT
strategy http_rsp: 1023
request completed, request_id: 1023 instrument:XTZUSDT
strategy http_rsp: 1024
request completed, request_id: 1024 instrument:SYSUSDT
strategy http_rsp: 1025
request completed, request_id: 1025 instrument:SUPERUSDT
strategy http_rsp: 1026
request completed, request_id: 1026 instrument:FLOCKUSDT
strategy http_rsp: 1027
request completed, request_id: 1027 instrument:PUMPBTCUSDT
strategy http_rsp: 1028
request completed, request_id: 1028 instrument:AEVOUSDT
strategy http_rsp: 1029
request completed, request_id: 1029 instrument:GRIFFAINUSDT
strategy http_rsp: 1030
request completed, request_id: 1030 instrument:VIRTUALUSDT
strategy http_rsp: 1031
request completed, request_id: 1031 instrument:XCHUSDT
strategy http_rsp: 1032
request completed, request_id: 1032 instrument:10000SATSUSDT
strategy http_rsp: 1033
request completed, request_id: 1033 instrument:BANKUSDT
strategy http_rsp: 1034
request completed, request_id: 1034 instrument:APEUSDT
strategy http_rsp: 1035
request completed, request_id: 1035 instrument:FHEUSDT
strategy http_rsp: 1036
request completed, request_id: 1036 instrument:HUMAUSDT
strategy http_rsp: 1037
request completed, request_id: 1037 instrument:OGNUSDT
strategy http_rsp: 1038
request completed, request_id: 1038 instrument:DOGSUSDT
strategy http_rsp: 1039
strategy request_id: 1040 ONEUSDT
fetch_klines, request_id: 1040 instrument: ONEUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1041 AKTUSDT
fetch_klines, request_id: 1041 instrument: AKTUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1042 SPECUSDT
fetch_klines, request_id: 1042 instrument: SPECUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1043 GOATUSDT
fetch_klines, request_id: 1043 instrument: GOATUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1044 FORTHUSDT
fetch_klines, request_id: 1044 instrument: FORTHUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1045 WLDUSDT
fetch_klines, request_id: 1045 instrument: WLDUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1046 RIFUSDT
fetch_klines, request_id: 1046 instrument: RIFUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1047 SKATEUSDT
fetch_klines, request_id: 1047 instrument: SKATEUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1048 TAIUSDT
fetch_klines, request_id: 1048 instrument: TAIUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1049 XAIUSDT
fetch_klines, request_id: 1049 instrument: XAIUSDT start_time: ************* end_time: ************* kline_cnt: 1000
strategy request_id: 1050 APTUSDT
fetch_klines, request_id: 1050 instrument: APTUSDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
strategy request_id: 1051 LTCUSDT
fetch_klines, request_id: 1051 instrument: LTCUSDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
strategy request_id: 1052 OMUSDT
fetch_klines, request_id: 1052 instrument: OMUSDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
strategy request_id: 1053 API3USDT
fetch_klines, request_id: 1053 instrument: API3USDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
strategy request_id: 1054 SOPHUSDT
fetch_klines, request_id: 1054 instrument: SOPHUSDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
strategy request_id: 1055 LUMIAUSDT
fetch_klines, request_id: 1055 instrument: LUMIAUSDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
strategy request_id: 1056 PENDLEUSDT
fetch_klines, request_id: 1056 instrument: PENDLEUSDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
strategy request_id: 1057 DYMUSDT
fetch_klines, request_id: 1057 instrument: DYMUSDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
strategy request_id: 1058 OXTUSDT
fetch_klines, request_id: 1058 instrument: OXTUSDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
strategy request_id: 1059 WIFUSDT
fetch_klines, request_id: 1059 instrument: WIFUSDT start_time: ************* end_time: 1749824971591 kline_cnt: 1000
request completed, request_id: 1039 instrument:ANKRUSDT
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Time range request completed: received total 29 klines from ************* to ************* (last batch size: 30)
Strategy stopping
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
Time range request completed: received total 29 klines from ************* to 1749824971591 (last batch size: 30)
strategy http_rsp: 1040
request completed, request_id: 1040 instrument:ONEUSDT
strategy http_rsp: 1041
request completed, request_id: 1041 instrument:AKTUSDT
strategy http_rsp: 1042
request completed, request_id: 1042 instrument:SPECUSDT
strategy http_rsp: 1043
request completed, request_id: 1043 instrument:GOATUSDT
strategy http_rsp: 1044
request completed, request_id: 1044 instrument:FORTHUSDT
strategy http_rsp: 1045
request completed, request_id: 1045 instrument:WLDUSDT
strategy http_rsp: 1046
request completed, request_id: 1046 instrument:RIFUSDT
strategy http_rsp: 1047
request completed, request_id: 1047 instrument:SKATEUSDT
strategy http_rsp: 1048
request completed, request_id: 1048 instrument:TAIUSDT
strategy http_rsp: 1049
request completed, request_id: 1049 instrument:XAIUSDT
strategy http_rsp: 1050
request completed, request_id: 1050 instrument:APTUSDT
strategy http_rsp: 1051
request completed, request_id: 1051 instrument:LTCUSDT
strategy http_rsp: 1052
request completed, request_id: 1052 instrument:OMUSDT
strategy http_rsp: 1053
request completed, request_id: 1053 instrument:API3USDT
strategy http_rsp: 1054
request completed, request_id: 1054 instrument:SOPHUSDT
strategy http_rsp: 1055
request completed, request_id: 1055 instrument:LUMIAUSDT
strategy http_rsp: 1056
request completed, request_id: 1056 instrument:PENDLEUSDT
strategy http_rsp: 1057
request completed, request_id: 1057 instrument:DYMUSDT
strategy http_rsp: 1058
request completed, request_id: 1058 instrument:OXTUSDT
strategy http_rsp: 1059
strategy request_id: 1060 GORKUSDT
fetch_klines, request_id: 1060 instrument: GORKUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1061 10000LADYSUSDT
fetch_klines, request_id: 1061 instrument: 10000LADYSUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1062 HYPEUSDT
fetch_klines, request_id: 1062 instrument: HYPEUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1063 FUELUSDT
fetch_klines, request_id: 1063 instrument: FUELUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1064 1000BONKUSDT
fetch_klines, request_id: 1064 instrument: 1000BONKUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1065 SEIUSDT
fetch_klines, request_id: 1065 instrument: SEIUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1066 DRIFTUSDT
fetch_klines, request_id: 1066 instrument: DRIFTUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1067 SOLUSDT
fetch_klines, request_id: 1067 instrument: SOLUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1068 L3USDT
fetch_klines, request_id: 1068 instrument: L3USDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1069 SOLOUSDT
fetch_klines, request_id: 1069 instrument: SOLOUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1070 ARPAUSDT
fetch_klines, request_id: 1070 instrument: ARPAUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1071 AVAXUSDT
fetch_klines, request_id: 1071 instrument: AVAXUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1072 IOTXUSDT
fetch_klines, request_id: 1072 instrument: IOTXUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1073 XAUTUSDT
fetch_klines, request_id: 1073 instrument: XAUTUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1074 PENGUUSDT
fetch_klines, request_id: 1074 instrument: PENGUUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1075 SUSHIUSDT
fetch_klines, request_id: 1075 instrument: SUSHIUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1076 EIGENUSDT
fetch_klines, request_id: 1076 instrument: EIGENUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1077 1000XECUSDT
fetch_klines, request_id: 1077 instrument: 1000XECUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1078 ENJUSDT
fetch_klines, request_id: 1078 instrument: ENJUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
strategy request_id: 1079 DODOUSDT
fetch_klines, request_id: 1079 instrument: DODOUSDT start_time: ************* end_time: 1749824973589 kline_cnt: 1000
request completed, request_id: 1059 instrument:WIFUSDT
runner stop
strategy desc
