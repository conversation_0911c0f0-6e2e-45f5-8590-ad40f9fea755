exchange:bybit instrument_idx_config_path:../config/instrument_config/bybit_ins_map.json instrument_info_config_path:../config/instrument_config/bybit_future_infos.json
../log/fix_md_fast_trader_elite.log
[INFO] [2025-06-13 22:29:35.511] [bybit_md_proxy.cc:48] [init] [bybit_md_proxy_0] Initializing bybit_md_proxy with log file: ../log/md//bybit_md_proxy_0.log
[INFO] [2025-06-13 22:29:35.513] [bybit_md_proxy.cc:65] [init] [bybit_md_proxy_0] bybit_md_proxy init
add_adapter md_id:0
my_strategy_so::create
[INFO] [2025-06-13 22:29:35.567] [bybit_md_proxy.cc:108] [do_ws_connect] [bybit_md_proxy_0] bybit_md_proxy do_ws_connect
async_connect
post force_close
[INFO] [2025-06-13 22:29:35.568] [bybit_md_proxy.cc:97] [connect] [bybit_md_proxy_0] bybit_md_proxy connect
force_close
post reset
register_md_inner_spi
on start, strategy_name: fix_md_tool
parse_config:{"auto_fix":false,"output_shm_path":"/home/<USER>/git/fast_trader_elite_pkg/main/kline_data_fix.shm","shm_path":"/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm"}
post async_connect_inner
do reset
async_resolve
on_resolve system:0 ec_msg:Success
on_connectsystem:0 ec_msg:Success
on_ssl_handshake system:0 ec_msg:Success
on_ws_handshake system:0 ec_msg:Success
[INFO] [2025-06-13 22:29:36.728] [bybit_md_proxy.cc:303] [on_connect] [bybit_md_proxy_0] bybit_md_proxy reconnected, subscribing all codes
[INFO] [2025-06-13 22:29:36.728] [bybit_md_proxy.cc:171] [subscribe] [bybit_md_proxy_0] bybit_md_proxy subscribe: received 0 codes, new codes: 0, total codes: 0 force:false
[INFO] [2025-06-13 22:29:36.728] [bybit_md_proxy.cc:178] [subscribe] [bybit_md_proxy_0] bybit_md_proxy: full subscription with 0 codes
Strategy stopping
