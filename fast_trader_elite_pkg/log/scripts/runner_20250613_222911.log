2025-06-13 22:29:11,158 - INFO - 所有路径检查通过
2025-06-13 22:29:11,158 - INFO - === 第1步：运行auto_instrument.sh脚本 ===
2025-06-13 22:29:11,158 - INFO - 运行auto_instrument.sh脚本...
2025-06-13 22:29:11,158 - INFO - 命令: sh auto_instrument.sh
2025-06-13 22:29:11,158 - INFO - 工作目录: /home/<USER>/git/fast_trader_elite_pkg/auto_instrument
2025-06-13 22:29:11,158 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/auto_instrument_20250613_222911.log
2025-06-13 22:29:11,160 - INFO - auto_instrument.sh脚本已启动，PID: 3832550
2025-06-13 22:29:12,846 - INFO - auto_instrument.sh脚本执行成功
2025-06-13 22:29:12,847 - INFO - auto_instrument.sh脚本执行完成
2025-06-13 22:29:12,847 - INFO - === 第2步：运行fix_md_tools程序 ===
2025-06-13 22:29:12,847 - INFO - 运行fix_md_tools程序...
2025-06-13 22:29:12,847 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/fix_md.json
2025-06-13 22:29:12,847 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/fix_md_tools_20250613_222912.log
2025-06-13 22:29:12,849 - INFO - fix_md_tools程序已启动，PID: 3832561
2025-06-13 22:29:14,620 - INFO - fix_md_tools程序执行成功
2025-06-13 22:29:14,620 - INFO - fix_md_tools程序执行完成
2025-06-13 22:29:14,620 - INFO - === 第3步：运行kline_feed_shm_v1程序（常驻进程）===
2025-06-13 22:29:14,621 - INFO - 启动kline_feed_shm_v1程序...
2025-06-13 22:29:14,621 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/kline_feed_shm.json
2025-06-13 22:29:14,621 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/kline_feed_shm_20250613_222914.log
2025-06-13 22:29:14,623 - INFO - kline_feed_shm_v1程序已启动，PID: 3832573
2025-06-13 22:29:14,623 - WARNING - 共享内存K线更新超时: 1694.62秒 > 64秒
2025-06-13 22:29:14,623 - ERROR - 检测到共享内存更新超时，准备重启所有程序
2025-06-13 22:29:14,623 - INFO - 终止kline_feed_shm_v1进程
2025-06-13 22:29:14,624 - INFO - 所有程序已按顺序启动完成，kline_feed_shm_v1正在运行
2025-06-13 22:29:14,624 - INFO - 共享内存监控已启用，超时阈值: 64秒
2025-06-13 22:29:14,624 - INFO - 按Ctrl+C退出
2025-06-13 22:29:14,625 - INFO - 开始重启所有程序...
2025-06-13 22:29:14,625 - INFO - === 重启程序：第1步 - 运行auto_instrument.sh ===
2025-06-13 22:29:14,625 - INFO - 运行auto_instrument.sh脚本...
2025-06-13 22:29:14,625 - INFO - 命令: sh auto_instrument.sh
2025-06-13 22:29:14,625 - INFO - 工作目录: /home/<USER>/git/fast_trader_elite_pkg/auto_instrument
2025-06-13 22:29:14,625 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/auto_instrument_20250613_222914.log
2025-06-13 22:29:14,627 - INFO - auto_instrument.sh脚本已启动，PID: 3832575
2025-06-13 22:29:16,365 - INFO - auto_instrument.sh脚本执行成功
2025-06-13 22:29:16,365 - INFO - === 重启程序：第2步 - 运行fix_md_tools ===
2025-06-13 22:29:16,366 - INFO - 运行fix_md_tools程序...
2025-06-13 22:29:16,366 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/fix_md.json
2025-06-13 22:29:16,366 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/fix_md_tools_20250613_222916.log
2025-06-13 22:29:16,367 - INFO - fix_md_tools程序已启动，PID: 3832590
2025-06-13 22:29:18,152 - INFO - fix_md_tools程序执行成功
2025-06-13 22:29:18,153 - INFO - === 重启程序：第3步 - 运行kline_feed_shm_v1 ===
2025-06-13 22:29:18,153 - INFO - 启动kline_feed_shm_v1程序...
2025-06-13 22:29:18,153 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/kline_feed_shm.json
2025-06-13 22:29:18,153 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/kline_feed_shm_20250613_222918.log
2025-06-13 22:29:18,154 - INFO - kline_feed_shm_v1程序已启动，PID: 3832619
2025-06-13 22:29:18,155 - INFO - 所有程序重启完成
2025-06-13 22:29:23,160 - WARNING - 共享内存K线更新超时: 1703.16秒 > 64秒
2025-06-13 22:29:23,160 - ERROR - 检测到共享内存更新超时，准备重启所有程序
2025-06-13 22:29:23,160 - INFO - 终止kline_feed_shm_v1进程
2025-06-13 22:29:24,201 - INFO - 开始重启所有程序...
2025-06-13 22:29:24,202 - INFO - === 重启程序：第1步 - 运行auto_instrument.sh ===
2025-06-13 22:29:24,202 - INFO - 运行auto_instrument.sh脚本...
2025-06-13 22:29:24,202 - INFO - 命令: sh auto_instrument.sh
2025-06-13 22:29:24,202 - INFO - 工作目录: /home/<USER>/git/fast_trader_elite_pkg/auto_instrument
2025-06-13 22:29:24,202 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/auto_instrument_20250613_222924.log
2025-06-13 22:29:24,203 - INFO - auto_instrument.sh脚本已启动，PID: 3832641
2025-06-13 22:29:25,956 - INFO - auto_instrument.sh脚本执行成功
2025-06-13 22:29:25,957 - INFO - === 重启程序：第2步 - 运行fix_md_tools ===
2025-06-13 22:29:25,957 - INFO - 运行fix_md_tools程序...
2025-06-13 22:29:25,957 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/fix_md.json
2025-06-13 22:29:25,957 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/fix_md_tools_20250613_222925.log
2025-06-13 22:29:25,969 - INFO - fix_md_tools程序已启动，PID: 3832658
2025-06-13 22:29:27,706 - INFO - fix_md_tools程序执行成功
2025-06-13 22:29:27,706 - INFO - === 重启程序：第3步 - 运行kline_feed_shm_v1 ===
2025-06-13 22:29:27,706 - INFO - 启动kline_feed_shm_v1程序...
2025-06-13 22:29:27,706 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/kline_feed_shm.json
2025-06-13 22:29:27,706 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/kline_feed_shm_20250613_222927.log
2025-06-13 22:29:27,708 - INFO - kline_feed_shm_v1程序已启动，PID: 3832678
2025-06-13 22:29:27,708 - INFO - 所有程序重启完成
2025-06-13 22:29:32,712 - WARNING - 共享内存K线更新超时: 1712.71秒 > 64秒
2025-06-13 22:29:32,712 - ERROR - 检测到共享内存更新超时，准备重启所有程序
2025-06-13 22:29:32,712 - INFO - 终止kline_feed_shm_v1进程
2025-06-13 22:29:33,783 - INFO - 开始重启所有程序...
2025-06-13 22:29:33,783 - INFO - === 重启程序：第1步 - 运行auto_instrument.sh ===
2025-06-13 22:29:33,784 - INFO - 运行auto_instrument.sh脚本...
2025-06-13 22:29:33,784 - INFO - 命令: sh auto_instrument.sh
2025-06-13 22:29:33,784 - INFO - 工作目录: /home/<USER>/git/fast_trader_elite_pkg/auto_instrument
2025-06-13 22:29:33,784 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/auto_instrument_20250613_222933.log
2025-06-13 22:29:33,785 - INFO - auto_instrument.sh脚本已启动，PID: 3832698
2025-06-13 22:29:35,474 - INFO - auto_instrument.sh脚本执行成功
2025-06-13 22:29:35,475 - INFO - === 重启程序：第2步 - 运行fix_md_tools ===
2025-06-13 22:29:35,475 - INFO - 运行fix_md_tools程序...
2025-06-13 22:29:35,475 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/fix_md.json
2025-06-13 22:29:35,475 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/fix_md_tools_20250613_222935.log
2025-06-13 22:29:35,477 - INFO - fix_md_tools程序已启动，PID: 3832709
2025-06-13 22:29:36,839 - INFO - 收到信号 2，正在退出...
2025-06-13 22:29:36,839 - INFO - 正在停止所有进程...
2025-06-13 22:29:36,839 - INFO - 停止fix_md_tools程序 (PID: 3832709)
2025-06-13 22:29:37,890 - INFO - fix_md_tools程序执行成功
2025-06-13 22:29:37,890 - INFO - === 重启程序：第3步 - 运行kline_feed_shm_v1 ===
2025-06-13 22:29:37,890 - INFO - 启动kline_feed_shm_v1程序...
2025-06-13 22:29:37,890 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/kline_feed_shm.json
2025-06-13 22:29:37,890 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/kline_feed_shm_20250613_222937.log
2025-06-13 22:29:37,894 - INFO - kline_feed_shm_v1程序已启动，PID: 3832731
2025-06-13 22:29:37,894 - INFO - 所有程序重启完成
2025-06-13 22:29:37,905 - INFO - fix_md_tools程序已正常退出
