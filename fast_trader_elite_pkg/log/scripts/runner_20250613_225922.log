2025-06-13 22:59:22,477 - INFO - 所有路径检查通过
2025-06-13 22:59:22,477 - INFO - === 第1步：运行auto_instrument.sh脚本 ===
2025-06-13 22:59:22,477 - INFO - 运行auto_instrument.sh脚本...
2025-06-13 22:59:22,477 - INFO - 命令: sh auto_instrument.sh
2025-06-13 22:59:22,477 - INFO - 工作目录: /home/<USER>/git/fast_trader_elite_pkg/auto_instrument
2025-06-13 22:59:22,477 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/auto_instrument_20250613_225922.log
2025-06-13 22:59:22,481 - INFO - auto_instrument.sh脚本已启动，PID: 3840207
2025-06-13 22:59:24,411 - INFO - auto_instrument.sh脚本执行成功
2025-06-13 22:59:24,411 - INFO - auto_instrument.sh脚本执行完成
2025-06-13 22:59:24,412 - INFO - === 第2步：运行fix_md_tools程序 ===
2025-06-13 22:59:24,412 - INFO - 运行fix_md_tools程序...
2025-06-13 22:59:24,412 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/fix_md.json
2025-06-13 22:59:24,412 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/fix_md_tools_20250613_225924.log
2025-06-13 22:59:24,415 - INFO - fix_md_tools程序已启动，PID: 3840218
2025-06-13 22:59:26,479 - INFO - fix_md_tools程序执行成功
2025-06-13 22:59:26,496 - INFO - fix_md_tools程序执行完成
2025-06-13 22:59:26,497 - INFO - === 第3步：运行kline_feed_shm_v1程序（常驻进程）===
2025-06-13 22:59:26,500 - INFO - 启动kline_feed_shm_v1程序...
2025-06-13 22:59:26,501 - INFO - 命令: /home/<USER>/git/fast_trader_elite_pkg/main/fast_trader_elite /home/<USER>/git/fast_trader_elite_pkg/config/kline_feed_shm.json
2025-06-13 22:59:26,501 - INFO - 日志文件: /home/<USER>/git/fast_trader_elite_pkg/log/scripts/kline_feed_shm_20250613_225926.log
2025-06-13 22:59:26,511 - INFO - kline_feed_shm_v1程序已启动，PID: 3840234
2025-06-13 22:59:26,520 - DEBUG - 共享内存程序状态: STOP
2025-06-13 22:59:26,520 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 22:59:26,520 - INFO - 所有程序已按顺序启动完成，kline_feed_shm_v1正在运行
2025-06-13 22:59:26,520 - INFO - 共享内存监控已启用，超时阈值: 64秒
2025-06-13 22:59:26,520 - INFO - 按Ctrl+C退出
2025-06-13 22:59:31,525 - DEBUG - 共享内存程序状态: STOP
2025-06-13 22:59:31,525 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 22:59:36,530 - DEBUG - 共享内存程序状态: STOP
2025-06-13 22:59:36,530 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 22:59:41,535 - DEBUG - 共享内存程序状态: STOP
2025-06-13 22:59:41,536 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 22:59:46,541 - DEBUG - 共享内存程序状态: STOP
2025-06-13 22:59:46,541 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 22:59:51,546 - DEBUG - 共享内存程序状态: STOP
2025-06-13 22:59:51,546 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 22:59:56,550 - DEBUG - 共享内存程序状态: STOP
2025-06-13 22:59:56,550 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 23:00:01,555 - DEBUG - 共享内存程序状态: STOP
2025-06-13 23:00:01,555 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 23:00:06,560 - DEBUG - 共享内存程序状态: STOP
2025-06-13 23:00:06,560 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 23:00:11,565 - DEBUG - 共享内存程序状态: STOP
2025-06-13 23:00:11,565 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 23:00:16,570 - DEBUG - 共享内存程序状态: STOP
2025-06-13 23:00:16,571 - DEBUG - 程序状态为STOP，跳过超时检测
2025-06-13 23:00:21,576 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:00:21,578 - DEBUG - 共享内存K线收盘时间差: 21.58秒 (当前: 1749826821576, K线收盘: 1749826800000, 状态: RUNNING)
2025-06-13 23:00:26,583 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:00:26,583 - DEBUG - 共享内存K线收盘时间差: 26.58秒 (当前: 1749826826583, K线收盘: 1749826800000, 状态: RUNNING)
2025-06-13 23:00:31,588 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:00:31,588 - DEBUG - 共享内存K线收盘时间差: 31.59秒 (当前: 1749826831588, K线收盘: 1749826800000, 状态: RUNNING)
2025-06-13 23:00:36,593 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:00:36,593 - DEBUG - 共享内存K线收盘时间差: 36.59秒 (当前: 1749826836593, K线收盘: 1749826800000, 状态: RUNNING)
2025-06-13 23:00:41,598 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:00:41,599 - DEBUG - 共享内存K线收盘时间差: 41.60秒 (当前: 1749826841599, K线收盘: 1749826800000, 状态: RUNNING)
2025-06-13 23:00:46,604 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:00:46,604 - DEBUG - 共享内存K线收盘时间差: 46.60秒 (当前: 1749826846604, K线收盘: 1749826800000, 状态: RUNNING)
2025-06-13 23:00:51,607 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:00:51,607 - DEBUG - 共享内存K线收盘时间差: 51.61秒 (当前: 1749826851607, K线收盘: 1749826800000, 状态: RUNNING)
2025-06-13 23:00:56,612 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:00:56,612 - DEBUG - 共享内存K线收盘时间差: 56.61秒 (当前: 1749826856612, K线收盘: 1749826800000, 状态: RUNNING)
2025-06-13 23:01:01,617 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:01,617 - DEBUG - 共享内存K线收盘时间差: 1.62秒 (当前: 1749826861617, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:06,622 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:06,622 - DEBUG - 共享内存K线收盘时间差: 6.62秒 (当前: 1749826866622, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:11,627 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:11,627 - DEBUG - 共享内存K线收盘时间差: 11.63秒 (当前: 1749826871627, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:16,632 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:16,632 - DEBUG - 共享内存K线收盘时间差: 16.63秒 (当前: 1749826876632, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:21,637 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:21,637 - DEBUG - 共享内存K线收盘时间差: 21.64秒 (当前: 1749826881637, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:26,642 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:26,643 - DEBUG - 共享内存K线收盘时间差: 26.64秒 (当前: 1749826886643, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:31,648 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:31,648 - DEBUG - 共享内存K线收盘时间差: 31.65秒 (当前: 1749826891648, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:36,654 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:36,654 - DEBUG - 共享内存K线收盘时间差: 36.65秒 (当前: 1749826896654, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:41,656 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:41,657 - DEBUG - 共享内存K线收盘时间差: 41.66秒 (当前: 1749826901657, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:46,661 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:46,661 - DEBUG - 共享内存K线收盘时间差: 46.66秒 (当前: 1749826906661, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:51,666 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:51,666 - DEBUG - 共享内存K线收盘时间差: 51.67秒 (当前: 1749826911666, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:01:56,669 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:01:56,669 - DEBUG - 共享内存K线收盘时间差: 56.67秒 (当前: 1749826916669, K线收盘: 1749826860000, 状态: RUNNING)
2025-06-13 23:02:01,674 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:01,674 - DEBUG - 共享内存K线收盘时间差: 1.67秒 (当前: 1749826921674, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:06,678 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:06,678 - DEBUG - 共享内存K线收盘时间差: 6.68秒 (当前: 1749826926678, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:11,683 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:11,683 - DEBUG - 共享内存K线收盘时间差: 11.68秒 (当前: 1749826931683, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:16,688 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:16,689 - DEBUG - 共享内存K线收盘时间差: 16.69秒 (当前: 1749826936689, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:21,694 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:21,694 - DEBUG - 共享内存K线收盘时间差: 21.69秒 (当前: 1749826941694, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:26,700 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:26,700 - DEBUG - 共享内存K线收盘时间差: 26.70秒 (当前: 1749826946700, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:31,705 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:31,705 - DEBUG - 共享内存K线收盘时间差: 31.70秒 (当前: 1749826951705, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:36,711 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:36,711 - DEBUG - 共享内存K线收盘时间差: 36.71秒 (当前: 1749826956711, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:41,716 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:41,717 - DEBUG - 共享内存K线收盘时间差: 41.72秒 (当前: 1749826961717, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:46,720 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:46,721 - DEBUG - 共享内存K线收盘时间差: 46.72秒 (当前: 1749826966721, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:51,726 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:51,726 - DEBUG - 共享内存K线收盘时间差: 51.73秒 (当前: 1749826971726, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:02:56,732 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:02:56,732 - DEBUG - 共享内存K线收盘时间差: 56.73秒 (当前: 1749826976732, K线收盘: 1749826920000, 状态: RUNNING)
2025-06-13 23:03:01,736 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:01,736 - DEBUG - 共享内存K线收盘时间差: 1.74秒 (当前: 1749826981736, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:06,739 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:06,739 - DEBUG - 共享内存K线收盘时间差: 6.74秒 (当前: 1749826986739, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:11,744 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:11,745 - DEBUG - 共享内存K线收盘时间差: 11.74秒 (当前: 1749826991745, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:16,750 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:16,750 - DEBUG - 共享内存K线收盘时间差: 16.75秒 (当前: 1749826996750, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:21,755 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:21,755 - DEBUG - 共享内存K线收盘时间差: 21.75秒 (当前: 1749827001755, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:26,760 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:26,761 - DEBUG - 共享内存K线收盘时间差: 26.76秒 (当前: 1749827006761, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:31,766 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:31,766 - DEBUG - 共享内存K线收盘时间差: 31.77秒 (当前: 1749827011766, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:36,771 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:36,772 - DEBUG - 共享内存K线收盘时间差: 36.77秒 (当前: 1749827016772, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:41,777 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:41,777 - DEBUG - 共享内存K线收盘时间差: 41.78秒 (当前: 1749827021777, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:46,779 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:46,779 - DEBUG - 共享内存K线收盘时间差: 46.78秒 (当前: 1749827026779, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:51,784 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:51,785 - DEBUG - 共享内存K线收盘时间差: 51.78秒 (当前: 1749827031785, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:03:56,790 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:03:56,790 - DEBUG - 共享内存K线收盘时间差: 56.79秒 (当前: 1749827036790, K线收盘: 1749826980000, 状态: RUNNING)
2025-06-13 23:04:01,794 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:01,795 - DEBUG - 共享内存K线收盘时间差: 1.79秒 (当前: 1749827041795, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:06,800 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:06,800 - DEBUG - 共享内存K线收盘时间差: 6.80秒 (当前: 1749827046800, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:11,805 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:11,806 - DEBUG - 共享内存K线收盘时间差: 11.81秒 (当前: 1749827051806, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:16,811 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:16,811 - DEBUG - 共享内存K线收盘时间差: 16.81秒 (当前: 1749827056811, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:21,817 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:21,817 - DEBUG - 共享内存K线收盘时间差: 21.82秒 (当前: 1749827061817, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:26,822 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:26,823 - DEBUG - 共享内存K线收盘时间差: 26.82秒 (当前: 1749827066822, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:31,828 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:31,828 - DEBUG - 共享内存K线收盘时间差: 31.83秒 (当前: 1749827071828, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:36,833 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:36,834 - DEBUG - 共享内存K线收盘时间差: 36.83秒 (当前: 1749827076834, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:41,839 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:41,839 - DEBUG - 共享内存K线收盘时间差: 41.84秒 (当前: 1749827081839, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:46,844 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:46,844 - DEBUG - 共享内存K线收盘时间差: 46.84秒 (当前: 1749827086844, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:51,849 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:51,850 - DEBUG - 共享内存K线收盘时间差: 51.85秒 (当前: 1749827091850, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:04:56,855 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:04:56,855 - DEBUG - 共享内存K线收盘时间差: 56.85秒 (当前: 1749827096855, K线收盘: 1749827040000, 状态: RUNNING)
2025-06-13 23:05:01,859 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:01,859 - DEBUG - 共享内存K线收盘时间差: 1.86秒 (当前: 1749827101859, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:06,864 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:06,864 - DEBUG - 共享内存K线收盘时间差: 6.86秒 (当前: 1749827106864, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:11,867 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:11,867 - DEBUG - 共享内存K线收盘时间差: 11.87秒 (当前: 1749827111867, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:16,869 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:16,870 - DEBUG - 共享内存K线收盘时间差: 16.87秒 (当前: 1749827116869, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:21,875 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:21,875 - DEBUG - 共享内存K线收盘时间差: 21.88秒 (当前: 1749827121875, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:26,880 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:26,880 - DEBUG - 共享内存K线收盘时间差: 26.88秒 (当前: 1749827126880, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:31,886 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:31,886 - DEBUG - 共享内存K线收盘时间差: 31.89秒 (当前: 1749827131886, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:36,891 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:36,891 - DEBUG - 共享内存K线收盘时间差: 36.89秒 (当前: 1749827136891, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:41,896 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:41,896 - DEBUG - 共享内存K线收盘时间差: 41.90秒 (当前: 1749827141896, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:46,900 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:46,900 - DEBUG - 共享内存K线收盘时间差: 46.90秒 (当前: 1749827146900, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:51,905 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:51,905 - DEBUG - 共享内存K线收盘时间差: 51.91秒 (当前: 1749827151905, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:05:56,911 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:05:56,911 - DEBUG - 共享内存K线收盘时间差: 56.91秒 (当前: 1749827156911, K线收盘: 1749827100000, 状态: RUNNING)
2025-06-13 23:06:01,916 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:01,916 - DEBUG - 共享内存K线收盘时间差: 1.92秒 (当前: 1749827161916, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:06,922 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:06,922 - DEBUG - 共享内存K线收盘时间差: 6.92秒 (当前: 1749827166922, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:11,924 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:11,924 - DEBUG - 共享内存K线收盘时间差: 11.92秒 (当前: 1749827171924, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:16,929 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:16,930 - DEBUG - 共享内存K线收盘时间差: 16.93秒 (当前: 1749827176930, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:21,931 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:21,931 - DEBUG - 共享内存K线收盘时间差: 21.93秒 (当前: 1749827181931, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:26,937 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:26,937 - DEBUG - 共享内存K线收盘时间差: 26.94秒 (当前: 1749827186937, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:31,942 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:31,943 - DEBUG - 共享内存K线收盘时间差: 31.94秒 (当前: 1749827191943, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:36,948 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:36,948 - DEBUG - 共享内存K线收盘时间差: 36.95秒 (当前: 1749827196948, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:41,953 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:41,954 - DEBUG - 共享内存K线收盘时间差: 41.95秒 (当前: 1749827201954, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:46,956 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:46,957 - DEBUG - 共享内存K线收盘时间差: 46.96秒 (当前: 1749827206957, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:51,962 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:51,962 - DEBUG - 共享内存K线收盘时间差: 51.96秒 (当前: 1749827211962, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:06:56,966 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:06:56,966 - DEBUG - 共享内存K线收盘时间差: 56.97秒 (当前: 1749827216966, K线收盘: 1749827160000, 状态: RUNNING)
2025-06-13 23:07:01,970 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:01,970 - DEBUG - 共享内存K线收盘时间差: 1.97秒 (当前: 1749827221970, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:06,972 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:06,972 - DEBUG - 共享内存K线收盘时间差: 6.97秒 (当前: 1749827226972, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:11,975 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:11,976 - DEBUG - 共享内存K线收盘时间差: 11.98秒 (当前: 1749827231976, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:16,981 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:16,981 - DEBUG - 共享内存K线收盘时间差: 16.98秒 (当前: 1749827236981, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:21,986 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:21,986 - DEBUG - 共享内存K线收盘时间差: 21.99秒 (当前: 1749827241986, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:26,991 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:26,992 - DEBUG - 共享内存K线收盘时间差: 26.99秒 (当前: 1749827246992, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:31,997 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:31,997 - DEBUG - 共享内存K线收盘时间差: 32.00秒 (当前: 1749827251997, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:37,003 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:37,003 - DEBUG - 共享内存K线收盘时间差: 37.00秒 (当前: 1749827257003, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:42,005 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:42,006 - DEBUG - 共享内存K线收盘时间差: 42.01秒 (当前: 1749827262006, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:47,011 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:47,011 - DEBUG - 共享内存K线收盘时间差: 47.01秒 (当前: 1749827267011, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:52,016 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:52,017 - DEBUG - 共享内存K线收盘时间差: 52.02秒 (当前: 1749827272017, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:07:57,022 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:07:57,022 - DEBUG - 共享内存K线收盘时间差: 57.02秒 (当前: 1749827277022, K线收盘: 1749827220000, 状态: RUNNING)
2025-06-13 23:08:02,027 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:02,027 - DEBUG - 共享内存K线收盘时间差: 2.03秒 (当前: 1749827282027, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:07,033 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:07,033 - DEBUG - 共享内存K线收盘时间差: 7.03秒 (当前: 1749827287033, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:12,038 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:12,038 - DEBUG - 共享内存K线收盘时间差: 12.04秒 (当前: 1749827292038, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:17,043 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:17,044 - DEBUG - 共享内存K线收盘时间差: 17.04秒 (当前: 1749827297044, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:22,044 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:22,044 - DEBUG - 共享内存K线收盘时间差: 22.04秒 (当前: 1749827302044, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:27,046 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:27,047 - DEBUG - 共享内存K线收盘时间差: 27.05秒 (当前: 1749827307047, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:32,048 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:32,048 - DEBUG - 共享内存K线收盘时间差: 32.05秒 (当前: 1749827312048, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:37,053 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:37,053 - DEBUG - 共享内存K线收盘时间差: 37.05秒 (当前: 1749827317053, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:42,057 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:42,058 - DEBUG - 共享内存K线收盘时间差: 42.06秒 (当前: 1749827322058, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:47,063 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:47,063 - DEBUG - 共享内存K线收盘时间差: 47.06秒 (当前: 1749827327063, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:52,068 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:52,069 - DEBUG - 共享内存K线收盘时间差: 52.07秒 (当前: 1749827332069, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:08:57,074 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:08:57,074 - DEBUG - 共享内存K线收盘时间差: 57.07秒 (当前: 1749827337074, K线收盘: 1749827280000, 状态: RUNNING)
2025-06-13 23:09:02,080 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:02,080 - DEBUG - 共享内存K线收盘时间差: 2.08秒 (当前: 1749827342080, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:07,082 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:07,082 - DEBUG - 共享内存K线收盘时间差: 7.08秒 (当前: 1749827347082, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:12,087 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:12,087 - DEBUG - 共享内存K线收盘时间差: 12.09秒 (当前: 1749827352087, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:17,092 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:17,092 - DEBUG - 共享内存K线收盘时间差: 17.09秒 (当前: 1749827357092, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:22,097 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:22,097 - DEBUG - 共享内存K线收盘时间差: 22.10秒 (当前: 1749827362097, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:27,099 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:27,100 - DEBUG - 共享内存K线收盘时间差: 27.10秒 (当前: 1749827367100, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:32,105 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:32,105 - DEBUG - 共享内存K线收盘时间差: 32.10秒 (当前: 1749827372105, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:37,111 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:37,111 - DEBUG - 共享内存K线收盘时间差: 37.11秒 (当前: 1749827377111, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:42,116 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:42,117 - DEBUG - 共享内存K线收盘时间差: 42.12秒 (当前: 1749827382117, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:47,122 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:47,122 - DEBUG - 共享内存K线收盘时间差: 47.12秒 (当前: 1749827387122, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:52,128 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:52,128 - DEBUG - 共享内存K线收盘时间差: 52.13秒 (当前: 1749827392128, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:09:57,131 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:09:57,131 - DEBUG - 共享内存K线收盘时间差: 57.13秒 (当前: 1749827397131, K线收盘: 1749827340000, 状态: RUNNING)
2025-06-13 23:10:02,136 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:10:02,136 - DEBUG - 共享内存K线收盘时间差: 2.14秒 (当前: 1749827402136, K线收盘: 1749827400000, 状态: RUNNING)
2025-06-13 23:10:07,137 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:10:07,137 - DEBUG - 共享内存K线收盘时间差: 7.14秒 (当前: 1749827407137, K线收盘: 1749827400000, 状态: RUNNING)
2025-06-13 23:10:12,143 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:10:12,143 - DEBUG - 共享内存K线收盘时间差: 12.14秒 (当前: 1749827412143, K线收盘: 1749827400000, 状态: RUNNING)
2025-06-13 23:10:17,147 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:10:17,147 - DEBUG - 共享内存K线收盘时间差: 17.15秒 (当前: 1749827417147, K线收盘: 1749827400000, 状态: RUNNING)
2025-06-13 23:10:22,152 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:10:22,153 - DEBUG - 共享内存K线收盘时间差: 22.15秒 (当前: 1749827422153, K线收盘: 1749827400000, 状态: RUNNING)
2025-06-13 23:10:27,155 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:10:27,155 - DEBUG - 共享内存K线收盘时间差: 27.16秒 (当前: 1749827427155, K线收盘: 1749827400000, 状态: RUNNING)
2025-06-13 23:10:32,158 - DEBUG - 共享内存程序状态: RUNNING
2025-06-13 23:10:32,158 - DEBUG - 共享内存K线收盘时间差: 32.16秒 (当前: 1749827432158, K线收盘: 1749827400000, 状态: RUNNING)
