=== 共享内存监控测试（包含程序状态）===
共享内存文件路径: /home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm

调试信息: magic=0x4b4c494e, version=1, contracts=528
调试信息: last_kline_close_time=1749825960000, last_update_timestamp=1749825961114076078
调试信息: program_state=0 (INIT)
✅ 共享内存文件格式正确

=== 共享内存头部信息 ===
魔数: 0x4b4c494e
版本: 1
最大合约数: 1000
每合约最大K线数: 13100
当前合约数: 528
最后K线收盘时间: 2025-06-13 22:46:00
最后更新时间戳: 2025-06-13 22:46:01.114
程序状态: INIT (0)

=== 超时检查结果 ===
K线收盘时间差: 0.00秒
超时阈值: 64秒
程序状态: INIT
检查原因: 程序状态为INIT，跳过超时检测
✅ K线更新正常
