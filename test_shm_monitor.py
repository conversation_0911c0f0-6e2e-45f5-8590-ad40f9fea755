#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享内存监控功能测试脚本（包含程序状态解析）
"""

import os
import sys
import ctypes
import mmap
import time
from datetime import datetime

# 程序状态枚举
class ShmProgramState:
    INIT = 0      # 初始化状态
    RUNNING = 1   # 正常运行状态
    STOP = 2      # 停止状态
    ERROR = 3     # 错误状态
    
    @staticmethod
    def to_string(state):
        state_map = {
            ShmProgramState.INIT: "INIT",
            ShmProgramState.RUNNING: "RUNNING", 
            ShmProgramState.STOP: "STOP",
            ShmProgramState.ERROR: "ERROR"
        }
        return state_map.get(state, f"UNKNOWN({state})")

# 定义与C++相同的数据结构
class KlineShmHeader(ctypes.Structure):
    _fields_ = [
        ("magic", ctypes.c_uint32),
        ("version", ctypes.c_uint32),
        ("max_contracts", ctypes.c_uint32),
        ("max_klines_per_contract", ctypes.c_uint32),
        ("contract_count", ctypes.c_uint32),
        ("last_kline_close_time", ctypes.c_uint64),
        ("last_update_timestamp", ctypes.c_uint64),
        ("reserved", ctypes.c_uint32 * 6)  # reserved[0] 用于存储程序状态
    ]

def read_shm_header(shm_file_path):
    """读取共享内存头部信息"""
    try:
        if not os.path.exists(shm_file_path):
            print(f"共享内存文件不存在: {shm_file_path}")
            return None
            
        # 使用mmap读取共享内存，与demo保持一致
        fd = os.open(shm_file_path, os.O_RDONLY)
        shm = mmap.mmap(fd, 0, mmap.MAP_SHARED, mmap.PROT_READ)
        os.close(fd)
        
        try:
            # 读取头部信息
            header_size = ctypes.sizeof(KlineShmHeader)
            header_data = shm[:header_size]
            header = KlineShmHeader.from_buffer_copy(header_data)
            
            # 解析程序状态（使用reserved[0]字段）
            program_state = header.reserved[0] if header.reserved[0] <= 3 else ShmProgramState.ERROR
            
            print(f"调试信息: magic=0x{header.magic:x}, version={header.version}, contracts={header.contract_count}")
            print(f"调试信息: last_kline_close_time={header.last_kline_close_time}, last_update_timestamp={header.last_update_timestamp}")
            print(f"调试信息: program_state={program_state} ({ShmProgramState.to_string(program_state)})")
            
            return {
                'magic': header.magic,
                'version': header.version, 
                'max_contracts': header.max_contracts,
                'max_klines_per_contract': header.max_klines_per_contract,
                'contract_count': header.contract_count,
                'last_kline_close_time': header.last_kline_close_time,
                'last_update_timestamp': header.last_update_timestamp,
                'program_state': program_state,
                'program_state_str': ShmProgramState.to_string(program_state)
            }
        finally:
            shm.close()
            
    except Exception as e:
        print(f"读取共享内存头部失败: {e}")
        return None

def format_timestamp(timestamp_ns):
    """格式化时间戳（纳秒）为可读字符串"""
    if timestamp_ns == 0:
        return "未设置"
    
    # 检查时间戳是否合理
    if timestamp_ns > 2**63 - 1 or timestamp_ns < 0:
        return f"无效时间戳: {timestamp_ns}"
    
    timestamp_s = timestamp_ns / 1e9
    
    # 检查转换后的时间戳是否合理
    if timestamp_s > 2**31 - 1 or timestamp_s < 0:
        return f"无效时间戳: {timestamp_ns} (转换后: {timestamp_s})"
    
    try:
        dt = datetime.fromtimestamp(timestamp_s)
        return dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 保留毫秒
    except (ValueError, OSError) as e:
        return f"时间戳解析错误: {timestamp_ns} ({e})"

def format_kline_time(timestamp_ms):
    """格式化K线时间戳（毫秒）为可读字符串"""
    if timestamp_ms == 0:
        return "未设置"
    
    # 检查时间戳是否合理（应该在合理的范围内）
    if timestamp_ms > 2**63 - 1 or timestamp_ms < 0:
        return f"无效时间戳: {timestamp_ms}"
    
    # 如果时间戳太大，可能是纳秒而不是毫秒
    if timestamp_ms > 1e15:  # 大于2001年的纳秒时间戳
        timestamp_s = timestamp_ms / 1e9  # 纳秒转秒
    else:
        timestamp_s = timestamp_ms / 1000  # 毫秒转秒
    
    # 检查转换后的时间戳是否合理
    if timestamp_s > 2**31 - 1 or timestamp_s < 0:
        return f"无效时间戳: {timestamp_ms} (转换后: {timestamp_s})"
    
    try:
        dt = datetime.fromtimestamp(timestamp_s)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except (ValueError, OSError) as e:
        return f"时间戳解析错误: {timestamp_ms} ({e})"

def check_timeout(last_kline_close_time_ms, program_state, timeout_seconds=64):
    """检查K线收盘时间是否超时（考虑程序状态）"""
    if last_kline_close_time_ms == 0:
        return False, 0, "K线时间为0"
    
    # 如果程序状态为INIT，不检测超时
    if program_state == ShmProgramState.INIT:
        return False, 0, "程序状态为INIT，跳过超时检测"
    
    # 如果程序状态为STOP或ERROR，也不检测超时
    if program_state in [ShmProgramState.STOP, ShmProgramState.ERROR]:
        return False, 0, f"程序状态为{ShmProgramState.to_string(program_state)}，跳过超时检测"
        
    current_time_ms = int(time.time() * 1000)
    time_diff_seconds = (current_time_ms - last_kline_close_time_ms) / 1000.0
    
    is_timeout = time_diff_seconds > timeout_seconds
    return is_timeout, time_diff_seconds, "正常检测"

def test_shm_monitor():
    """测试共享内存监控功能"""
    shm_file_path = "/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm"
    
    print("=== 共享内存监控测试（包含程序状态）===")
    print(f"共享内存文件路径: {shm_file_path}")
    print()
    
    # 读取头部信息
    header = read_shm_header(shm_file_path)
    if header is None:
        print("❌ 无法读取共享内存头部")
        return False
    
    # 验证魔数
    expected_magic = 0x4B4C494E  # "KLIN" in ASCII
    if header['magic'] != expected_magic:
        print(f"❌ 魔数不匹配，期望: {expected_magic:x}, 实际: {header['magic']:x}")
        return False
    
    print("✅ 共享内存文件格式正确")
    print()
    
    # 显示头部信息
    print("=== 共享内存头部信息 ===")
    print(f"魔数: 0x{header['magic']:x}")
    print(f"版本: {header['version']}")
    print(f"最大合约数: {header['max_contracts']}")
    print(f"每合约最大K线数: {header['max_klines_per_contract']}")
    print(f"当前合约数: {header['contract_count']}")
    print(f"最后K线收盘时间: {format_kline_time(header['last_kline_close_time'])}")
    print(f"最后更新时间戳: {format_timestamp(header['last_update_timestamp'])}")
    print(f"程序状态: {header['program_state_str']} ({header['program_state']})")
    print()
    
    # 检查超时（基于K线收盘时间和程序状态）
    is_timeout, time_diff, reason = check_timeout(header['last_kline_close_time'], header['program_state'])
    
    print("=== 超时检查结果 ===")
    print(f"K线收盘时间差: {time_diff:.2f}秒")
    print(f"超时阈值: 64秒")
    print(f"程序状态: {header['program_state_str']}")
    print(f"检查原因: {reason}")
    
    if is_timeout:
        print("❌ 检测到K线更新超时！")
    else:
        print("✅ K线更新正常")
    
    return True

def continuous_monitor():
    """持续监控模式"""
    shm_file_path = "/home/<USER>/git/fast_trader_elite_pkg/main/kline_data.shm"
    
    print("=== 持续监控模式（包含程序状态）===")
    print("按Ctrl+C退出")
    print()
    
    try:
        while True:
            header = read_shm_header(shm_file_path)
            if header is None:
                print(f"{datetime.now().strftime('%H:%M:%S')} - 无法读取共享内存")
            else:
                is_timeout, time_diff, reason = check_timeout(header['last_kline_close_time'], header['program_state'])
                status = "超时" if is_timeout else "正常"
                print(f"{datetime.now().strftime('%H:%M:%S')} - "
                      f"合约数: {header['contract_count']}, "
                      f"K线时间差: {time_diff:.1f}s, "
                      f"状态: {header['program_state_str']}, "
                      f"检测: {status}")
            
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\n监控已停止")

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--monitor":
        continuous_monitor()
    else:
        success = test_shm_monitor()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
